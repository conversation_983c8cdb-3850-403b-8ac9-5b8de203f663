import { Collapse, ConfigProvider } from 'antd';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { leadFeatureType, leadGapFeatureType, templateEditorLocateRectWaitTime } from '../../../../../common/const';
import _ from 'lodash';
import styled from 'styled-components';
import { sleep } from '../../../../../common/util';
import { useTranslation } from 'react-i18next';
import { useUpdateComponentMutation } from '../../../../../services/product';


const GroupByComponent = (props) => {
  const {
    filteredComponents,
    allFeatures,
    allComponentsCollapseRef,
    componentReevaluationResultMap,
    selectedCid,
    setSelectedCid,
    setSelectedFid,
    setSelectedAgentParam,
    setSelectedUngroupedFid,
    setRequiredLocateRect,
    setSelectedFeatureType,
    selectedFeatureType,
    setSelectedPackageNo,
    setSelectedPartNo,
    setSelectedScope,
    refetchAllComponents,
    allFeatureReevaluationResult,
    setSelectedArrayIndex,
    aggregatedReevaluationResult,
  } = props;

  const { t } = useTranslation();

  const [parsedComponentMap, setParsedComponentMap] = useState({});
  const [cidFeatureTypeToPassingInfo, setCidFeatureTypeToPassingInfo] = useState({});
  // const [activeKey, setActiveKey] = useState([]);
  const [parsedAggregateResult, setParsedAggregateResult] = useState({});

  const [updateComponent] = useUpdateComponentMutation();

  const handleConfirmUnhealthyComponent = async (componentObj) => {
    const payload = {
      ...componentObj,
      healthy: true,
      array_index: 0,
    };

    delete payload['color_map_uri'];
    delete payload['depth_map_uri'];
    delete payload['created_at'];
    delete payload['modified_at'];
    delete payload['can_group_by_package_no'];
    delete payload['can_group_by_part_no'];
    delete payload['array_index'];
    delete payload['cloned'];
    delete payload['designator'];
    delete payload['variation_for'];

    if (payload.feature_info) delete payload['feature_info'];

    const res = await updateComponent({
      body: payload,
      params: { allComponents: true },
    });

    if (res.error) {
      aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
      console.error(res.error.message);
      return;
    }

    await refetchAllComponents();
  };

  useEffect(() => {
    if (_.isEmpty(aggregatedReevaluationResult)) {
      setParsedAggregateResult({});
      return;
    }

    const newMap = {};
    for (const r of aggregatedReevaluationResult) {
      if (!_.isEmpty(r.package_no)) {
        newMap[`${r.package_no}`] = r;
      } else if (!_.isEmpty(r.part_no)) {
        newMap[`${r.part_no}`] = r;
      } else {
        newMap[`${r.component_id}`] = r;
      }
    }

    setParsedAggregateResult(newMap);
  }, [
    aggregatedReevaluationResult,
  ]);

  useEffect(() => {
    if (_.isEmpty(allFeatures) || _.isEmpty(filteredComponents) || _.isEmpty(allFeatureReevaluationResult)) {
      setParsedComponentMap({});
      return;
    };

    const fidToFeatureMap = _.keyBy(allFeatures, f => _.get(f, 'feature_id', 0));
    const cidFeatureTypeToPassingInfo = {}; // {cid}_{feature_type}: {num_passing: 0, num_failing: 0}

    for (const featureInferenceResult of allFeatureReevaluationResult) {
      const fid = _.get(featureInferenceResult, 'feature_id', 0);
      if (!_.isInteger(_.get(fidToFeatureMap, `${fid}.group_id`, 0))) continue;
      const cid = _.get(fidToFeatureMap, `${fid}.group_id`, 0);
      const featureType = _.get(fidToFeatureMap, `${fid}.feature_type`, '');
      const key = `${cid}_${featureType}`;
      if (!_.has(cidFeatureTypeToPassingInfo, key)) {
        cidFeatureTypeToPassingInfo[key] = {
          num_passing: 0,
          num_failing: 0,
        };
      }
      cidFeatureTypeToPassingInfo[key].num_passing += _.get(featureInferenceResult, 'num_passing', 0);
      cidFeatureTypeToPassingInfo[key].num_failing += _.get(featureInferenceResult, 'num_failing', 0);
    }

    // console.log('cidFeatureTypeToPassingInfo', cidFeatureTypeToPassingInfo);

    setCidFeatureTypeToPassingInfo(cidFeatureTypeToPassingInfo);

    // update parsed component map
    const newMap = {};
    const ridToFeaturesMap = {};

    for (const f of allFeatures) {
      if (_.isInteger(f.group_id)) {
        if (!_.has(ridToFeaturesMap, String(f.group_id))) {
          ridToFeaturesMap[String(f.group_id)] = {
            features: [f],
            type_to_agent_params: {
              [f.feature_type]: {
                ...f.line_item_params,
                sample_fid: f.feature_id,
              },
            },
          };
        } else {
          ridToFeaturesMap[String(f.group_id)].features.push(f);
          if (!_.has(ridToFeaturesMap[String(f.group_id)].type_to_agent_params, f.feature_type)) {
            ridToFeaturesMap[String(f.group_id)].type_to_agent_params[f.feature_type] = {
              ...f.line_item_params,
              sample_fid: f.feature_id,
            };
          }
        }
      }
    }

    for (const c of filteredComponents) {
      const rid = c.region_group_id;
      const featureInfo = _.get(ridToFeaturesMap, String(rid), {});
      newMap[String(rid)] = {
        ...c,
        feature_info: featureInfo,
      };
    }

    setParsedComponentMap(newMap);
  }, [
    filteredComponents,
    allFeatures,
    allFeatureReevaluationResult,
  ]);

  // in case user click component roi in scene
  // and scene does not have the scope context
  useEffect(() => {
    if (selectedCid === null) setSelectedScope(null);
    if (selectedCid !== null) setSelectedScope('component');
  }, [selectedCid]);

  return (
    <Fragment>
      <ConfigProvider
        theme={{
          components: {
            Collapse: {
              headerPadding: '0px 0 0px 8px',
              contentPadding: '0 0 0 8px',
            }
          }
        }}
      >
        <CustomCollapse
          ref={allComponentsCollapseRef}
          style={{ width: '100%' }}
          onChange={(keys) => {
            const locateComponent = async (cid) => {
              setSelectedPackageNo(null);
              setSelectedPartNo(null);
              setSelectedFeatureType(null);
              await sleep(templateEditorLocateRectWaitTime);

              setRequiredLocateRect({
                cid,
                fid: null,
              });
            };

            setSelectedFid(null);
            setSelectedUngroupedFid(null);
            setSelectedAgentParam(null);

            // console.log('keys', keys);

            if (_.isEmpty(keys)) {
              setSelectedCid(null);
              setSelectedScope(null);
              setSelectedArrayIndex(null);
            } else if (keys.length === 1) {
              setSelectedCid(Number(keys[0]));
              locateComponent(Number(keys[0]));
              setSelectedScope('component');
              // setSelectedArrayIndex(_.get(parsedComponentMap, `[${keys[0]}].array_index`, null));
              if (_.isInteger(_.get(parsedComponentMap, `[${keys[0]}].array_index`, null))) {
                setSelectedArrayIndex(0);
              } else {
                setSelectedArrayIndex(null);
              }
            } else {
              setSelectedCid(Number(keys[1]));
              locateComponent(Number(keys[1]));
              setSelectedScope('component');
              // setSelectedArrayIndex(_.get(parsedComponentMap, `[${keys[1]}].array_index`, null));
              if (_.isInteger(_.get(parsedComponentMap, `[${keys[1]}].array_index`, null))) {
                setSelectedArrayIndex(0);
              } else {
                setSelectedArrayIndex(null);
              }
            }
          }}
          activeKey={_.isNull(selectedCid) ? [] : [String(selectedCid)]}
          items={_.map(_.keys(parsedComponentMap), (cid) => {
            const c = _.get(parsedComponentMap, cid, {});
            return {
              key: String(_.get(c, 'region_group_id', 0)),
              label: <div className={`flex h-[32px] px-2 items-center gap-2 justify-between`}>
                {(() => {
                  const isHealthy = _.get(c, 'healthy', false);
                  if (!isHealthy) {
                    return (
                      <Fragment>
                        <div className='flex items-center gap-1'>
                          <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`}>
                            {_.get(c, 'designator')}
                          </span>
                        </div>
                        <div
                          className='flex flex-col justify-center items-center gap-2.5 shrink-0 cursor-pointer'
                          title={t('productDefine.confirmComponentInfo')}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleConfirmUnhealthyComponent(c);
                          }}
                        >
                          <img
                            src='/icn/unknown_gray.svg'
                            className='w-[12px] h-[12px]'
                            alt='unknown'
                          />
                        </div>
                      </Fragment>
                    );
                  }

                  const result = _.get(componentReevaluationResultMap, c.region_group_id, {});
                  const numFail = _.get(result, 'num_failing', 0);
                  const numPass = _.get(result, 'num_passing', 0);
                  const containsWhite = _.get(result, 'contains_white_feature', false) || !_.has(componentReevaluationResultMap, String(c.region_group_id)) || (numFail === 0 && numPass === 0);

                  if (numFail === 0 && containsWhite && numPass >= 0) {
                    return (
                      <div className='flex items-center gap-1'>
                        <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`}>
                          {_.get(c, 'designator')}
                        </span>
                      </div>
                    );
                  }

                  if (numFail === 0 && !containsWhite && numPass > 0) {
                    return (
                      <Fragment>
                        <div className='flex items-center gap-1'>
                          <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap text-[#57F2C4]`}>
                            {_.get(c, 'designator')}
                          </span>
                        </div>
                        <img src={'/icn/checkFilledCircle_green.svg'} className='w-[12px] h-[12px]' alt='status' />
                      </Fragment>
                    );
                  }

                  if (numFail > 0) {
                    return (
                      <Fragment>
                        <div className='flex items-center gap-1'>
                          <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap text-[#EB5E28]`}>
                            {_.get(c, 'designator')}
                          </span>
                        </div>
                        <img src={'/icn/failedCircled_red.svg'} className='w-[12px] h-[12px]' alt='status' />
                      </Fragment>
                    );
                  }
                })()}
              </div>,
              children:
                <ConfigProvider
                  theme={{
                    components: {
                      Collapse: {
                        headerPadding: '0 0 0 8px',
                        contentPadding: '0 0 0 8px',
                      }
                    }
                  }}
                >
                  <CustomFeatureCollapse
                    style={{ width: '100%' }}
                    onChange={(keys) => {
                      setSelectedAgentParam(null);

                      let featureType = null;

                      if (_.isEmpty(keys)) {
                        setSelectedFeatureType(null);
                      } else if (keys.length === 1) {
                        setSelectedFeatureType(keys[0]);
                        featureType = keys[0];
                        // setActiveKey([keys[0]]);
                      } else {
                        setSelectedFeatureType(keys[1]);
                        featureType = keys[1];
                        // setActiveKey([keys[1]]);
                      }

                      const fid = _.get(parsedComponentMap, `[${cid}].feature_info.type_to_agent_params.${featureType}.sample_fid`, null);

                      setSelectedFid(fid);
                      setRequiredLocateRect({
                        cid: Number(cid),
                        fid,
                      });
                    }}
                    expandIcon={() => null}
                    activeKey={_.isNull(selectedFeatureType) ? [] : [selectedFeatureType]}
                    // activeKey={activeKey}
                    items={_.reduce(
                      _.keys(_.get(c, 'feature_info.type_to_agent_params', {})),
                      (acc, type) => {
                        let numFail;
                        let numPass;

                        numFail = _.get(cidFeatureTypeToPassingInfo, `${c.region_group_id}_${type}.num_failing`, 0);
                        numPass = _.get(cidFeatureTypeToPassingInfo, `${c.region_group_id}_${type}.num_passing`, 0);
                        
                        if (type === leadFeatureType) {
                          const leadPass = _.get(parsedAggregateResult, `${c.region_group_id}.feature_type_aggregates._ic_lead.num_passing`, 0);
                          const leadFail = _.get(parsedAggregateResult, `${c.region_group_id}.feature_type_aggregates._ic_lead.num_failing`, 0);
                          const leadGapPass = _.get(parsedAggregateResult, `${c.region_group_id}.feature_type_aggregates._ic_gap.num_passing`, 0);
                          const leadGapFail = _.get(parsedAggregateResult, `${c.region_group_id}.feature_type_aggregates._ic_gap.num_failing`, 0);

                          const leadColor = leadFail > 0 ? '#EB5E28' : leadPass > 0 ? '#57F2C4' : '#fff';
                          const leadGapColor = leadGapFail > 0 ? '#EB5E28' : leadGapPass > 0 ? '#57F2C4' : '#fff';
                          const leadIcon = leadFail > 0 ? '/icn/failedCircled_red.svg' : leadPass > 0 ? '/icn/checkFilledCircle_green.svg' : '';
                          const leadGapIcon = leadGapFail > 0 ? '/icn/failedCircled_red.svg' : leadGapPass > 0 ? '/icn/checkFilledCircle_green.svg' : '';

                          return acc.concat([
                            {
                              key: leadFeatureType,
                              label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                                <span
                                  className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis
                                  whitespace-nowrap`}
                                  style={{ color: leadColor }}
                                >
                                  {t(`leadFeatureTypeText._ic_lead`)}
                                </span>
                                {leadIcon && <img src={leadIcon} className='w-[12px] h-[12px]' alt='status' />}
                              </div>,
                            },
                            {
                              key: leadGapFeatureType,
                              label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                                <span
                                  className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis
                                  whitespace-nowrap`}
                                  style={{ color: leadGapColor }}
                                >
                                  {t(`leadFeatureTypeText._ic_lead_gap`)}
                                </span>
                                {leadGapIcon && <img src={leadGapIcon} className='w-[12px] h-[12px]' alt='status' />}
                              </div>,
                            }
                          ]);
                        }
                        return acc.concat([{
                          key: type,
                          label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                            {(() => {
                              const numFail = _.get(cidFeatureTypeToPassingInfo, `${c.region_group_id}_${type}.num_failing`, 0);
                              const numPass = _.get(cidFeatureTypeToPassingInfo, `${c.region_group_id}_${type}.num_passing`, 0);
                              const color = numFail > 0 ? '#EB5E28' : numPass > 0 ? '#57F2C4' : '#fff';
                              const icon = numFail > 0 ? '/icn/failedCircled_red.svg' : numPass > 0 ? '/icn/checkFilledCircle_green.svg' : '';
                              return (
                                <Fragment>
                                  <div className='flex items-center gap-1'>
                                    <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`} style={{ color }}>
                                      {t(`leadFeatureTypeText.${type.startsWith('_text') ? '_text': type}`)}
                                    </span>
                                  </div>
                                  {icon && <img src={icon} className='w-[12px] h-[12px]' alt='status' />}
                                </Fragment>
                              );
                            })()}
                          </div>,
                        }]);
                      },
                      [],
                    )}
                  />
                </ConfigProvider>
            };
          })}
        />
      </ConfigProvider>
    </Fragment>
  );
};

const CustomCollapse = styled(Collapse)`
  border: none !important;
  border-radius: 0 !important;
  background-color: transparent !important;
  .ant-collapse-content-box {
    padding: 0 !important;
  }
  .ant-collapse-header[aria-expanded="true"] {
    background: rgba(86, 204, 242, 0.10) !important;
    color: #56CCF2 !important;
  }
  .ant-collapse-expand-icon {
    height: 32px !important;
  }
  .ant-collapse-header {
    &:hover {
      background: rgba(86, 204, 242, 0.10) !important;
    }
    border-bottom: 1px solid #ffffff0f !important;
  }
  .ant-collapse-content {
    border-radius: 0 !important;
    background-color: transparent !important;
  }
`;

const CustomFeatureCollapse = styled(Collapse)`
  border: none !important;
  border-radius: 0 !important;
  background-color: transparent !important;
  .ant-collapse-content-box {
    padding: 0 !important;
  }
  .ant-collapse-header[aria-expanded="true"] {
    background: rgba(86, 204, 242, 0.10) !important;
    color: #56CCF2 !important;
  }
  .ant-collapse-expand-icon {
    height: 32px !important;
  }
  .ant-collapse-header {
    padding-left: 16px !important;
    &:hover {
      background: rgba(86, 204, 242, 0.10) !important;
    }
  }
  .ant-collapse-content {
    padding-left: 24px !important;
    border-radius: 0 !important;
    background-color: transparent !important;
    border-bottom: 1px solid #ffffff0f !important;
    &:hover {
      background: rgba(86, 204, 242, 0.10) !important;
    }
  }
`;

export default GroupByComponent;