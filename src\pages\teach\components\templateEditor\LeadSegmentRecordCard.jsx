import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import { useTranslation } from 'react-i18next';
import { serverHost, highResoluCroppedDisplayMegaPixelCount, leadInspection3D, leadInspection2D, isAOI2DSMT } from '../../../../common/const';

const LeadSegmentRecordCard = (props) => {
  const {
    selected,
    selectedLineItemResult,
    leadIndex,
    setSelectedLineItemResultKey,
    setSelectedDetail,
    setSelectedLineItemResult,
    setSelectedLineItemResultParsedError,
    setSelectedFid,
    setSelectedCid,
    allFeatures,
    setRequiredLocateRect,
    clickFromTrainingSetCardRef,
    setSelectedArrayIndex,
    mmToPixelRatio,
    isPesudoColorDisplayed,
  } = props;

  const { t } = useTranslation();
  const [url, setUrl] = useState(null);
  const [latestPassStatus, setLatestPassStatus] = useState('empty');
  const [passStatus, setPassStatus] = useState(null);
  const [isGroudTrueGreen, setIsGroudTrueGreen] = useState(true);
  const [isTrainingExample, setIsTrainingExample] = useState(false);

  useEffect(() => {
    const firstAgent = _.get(selectedLineItemResult, '[0]', {});
    const feature = _.find(allFeatures, f => f.feature_id === _.get(firstAgent, 'feature_id', null));
    const agentName = isAOI2DSMT ? leadInspection2D : leadInspection3D;
    const leadCount = _.get(feature, `line_item_params.${agentName}.params.lead_count.param_int.value`, 0);
    const leadWidthMM = _.get(feature, `line_item_params.${agentName}.params.lead_width_mm.param_float.value`, 0);
    const pMin = _.get(firstAgent, 'roi.points[0]', { x: 0, y: 0 });
    const pMax = _.get(firstAgent, 'roi.points[1]', { x: 0, y: 0 });
    const width = pMax.x - pMin.x + 1;
    const leadWidth = leadWidthMM * mmToPixelRatio;
    const leadGapWidth = (width - leadCount * leadWidth) / (leadCount - 1);
    const xMin = pMin.x + leadIndex * leadWidth + leadIndex * leadGapWidth;
    const xMax = xMin + leadWidth;

    let tmpUrl = `${serverHost}/blob?type=${isPesudoColorDisplayed ? 'depth' : 'image'}`;
    tmpUrl += `&color_uri=${encodeURIComponent(_.get(firstAgent, 'component_color_map_uri', ''))}`;
    tmpUrl += `&depth_uri=${encodeURIComponent(_.get(firstAgent, 'component_depth_map_uri', ''))}`;
    const roundedXMin = Math.floor(xMin);
    const roundedXMax = Math.ceil(xMax);
    // tmpUrl += `&x_min=${roundedXMin}`;
    // tmpUrl += `&y_min=${Math.round(pMin.y)}`;
    // tmpUrl += `&x_max=${roundedXMax > roundedXMin ? roundedXMax : roundedXMin + 1}`;
    // tmpUrl += `&y_max=${Math.round(pMax.y)}`;

    tmpUrl += `&x_min=${Math.round(pMin.x)}`;
    tmpUrl += `&y_min=${Math.round(pMin.y)}`;
    tmpUrl += `&x_max=${Math.round(pMax.x)}`;
    tmpUrl += `&y_max=${Math.round(pMax.y)}`;

    tmpUrl += `&angle=${_.get(firstAgent, 'roi.angle', 0)}`;
    tmpUrl += `&max_megapixel=${highResoluCroppedDisplayMegaPixelCount}`;
    tmpUrl += `&t=${Date.now()}`;

    // download the full feature image then crop... for now
    // optimize this later if this slows down the rendering
    const downloadAndCrop = async () => {
      const res = await fetch(tmpUrl);
      const blob = await res.blob();
      const img = await createImageBitmap(blob);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = leadWidth;
      canvas.height = pMax.y - pMin.y;
      const sx = leadIndex * leadWidth + leadIndex * leadGapWidth;
      const sh = pMax.y - pMin.y;

      ctx.drawImage(img, sx, 0, leadWidth, sh, 0, 0, leadWidth, sh);
      const croppedBlob = await new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          resolve(blob);
        }, 'image/jpeg');
      });
      const croppedUrl = URL.createObjectURL(croppedBlob);
      setUrl(croppedUrl);
    };

    if (leadCount === 1) {
      setUrl(tmpUrl);
    } else {
      downloadAndCrop();
    }

    let flag;
    for (const a of selectedLineItemResult) {
      if (_.isEmpty(_.get(a, 'reevaluation_result', {}))) {
        flag = 'empty';
      } else {
        const parsedError = JSON.parse(_.get(a, 'reevaluation_result.error', '{}'));
        // flag = _.get(a, 'reevaluation_result.pass', false);
        flag = _.get(parsedError, 'lead_pass', false);
        if (!flag) break;
      }
    }

    setPassStatus(_.get(selectedLineItemResult, '[0].pass', false));
    setLatestPassStatus(flag);

    if (_.get(selectedLineItemResult, '[0].training_example', false)) {
      setIsGroudTrueGreen(true);
    } else {
      // search for agent result that has feedback
      for (const r of selectedLineItemResult) {
        setIsTrainingExample(_.get(r, 'training_example', false));
        if (!_.isEmpty(_.get(r, 'feedback', {}))) {
          const parsedError = JSON.parse(_.get(r, 'reevaluation_result.error', '{}'));
          const pass = _.get(parsedError, 'lead_pass', false);
          if (
            (pass && _.get(r, 'feedback.correct', false)) ||
            (!pass && !_.get(r, 'feedback.correct', false))
          ) {
            setIsGroudTrueGreen(true);
          } else {
            setIsGroudTrueGreen(false);
          }
          break;
        }
      }
      // set to pass status otherwise
      setIsGroudTrueGreen(_.get(r, 'pass', false));
    }
  }, [selectedLineItemResult, leadIndex, mmToPixelRatio, isPesudoColorDisplayed]);

  return (
    <div
      className={`flex p-1 flex-col gap-0.5 items-center justify-center rounded-[2px] border-[1px] border-gray-2 ${selected ? 'border-gray-3 bg-[#ffffff1a]' : 'bg-transparent'} cursor-pointer hover:bg-[#ffffff1a] transition-all duration-300`}
      onClick={() => {
        clickFromTrainingSetCardRef.current = true;
        setSelectedDetail(_.get(selectedLineItemResult, '[0].detail'));
        setSelectedLineItemResult(selectedLineItemResult);
        setSelectedArrayIndex(_.get(selectedLineItemResult, '[0].array_index', null));
        const dcid = _.get(_.find(allFeatures, f => f.feature_id === _.get(selectedLineItemResult, '[0].feature_id', null)), 'group_id', null);
        const cid = _.get(selectedLineItemResult, '[0].component_id', null);
        const fid = _.get(selectedLineItemResult, '[0].feature_id', null);
        setSelectedCid(dcid);
        setSelectedFid(fid);
        setSelectedLineItemResultKey(`${cid}_${fid}_${_.get(selectedLineItemResult, '[0].array_index', null)}`);
        setRequiredLocateRect({ cid: dcid, fid });
      }}
    >
      <img src={url} className='w-[120px] h-[120px] object-contain' alt='lead segment'/>

      <div className="flex h-2 items-center gap-0.5 self-stretch w-[120px] mx-auto">
        {/* <div className={`flex items-center gap-2.5 flex-[1_0_0] self-stretch ${passStatus ? '[background:var(--Failed,#27AE60)]' : '[background:var(--Failed,#FA4D56)]'} pl-3 pr-1.5 py-1`} /> */}
        <div className={`flex items-center gap-2.5 flex-[1_0_0] self-stretch ${
          isGroudTrueGreen ? '[background:var(--Failed,#27AE60)]' : '[background:var(--Failed,#FA4D56)]'
        } pl-3 pr-1.5 py-1`} />
        <div className={`flex items-center gap-2.5 flex-[1_0_0] self-stretch ${latestPassStatus === 'empty'
          ? 'border border-[color:var(--default-Gray-3,#828282)]'
          : (latestPassStatus
            ?
              '[background:var(--Failed,#27AE60)]'
            :
              '[background:var(--Failed,#FA4D56)]'
            ) } pl-3 pr-1.5 py-1`}
        />
      </div>

      <div className='flex py-1 px-2 items-center justify-center gap-1 self-stretch w-[136px]'>
        {isTrainingExample && (
          <img
            src='/icn/star_yellow.svg'
            alt='star'
            className='w-[10px] h-[10px]'
          />
        )}
        <span className={`font-source text-[10px] font-normal leading-[150%] tracking-[normal] ${latestPassStatus === 'empty' ? 'text-[#fff]' : (latestPassStatus ? 'text-[#6FCF97]' : 'text-[#FF6A6A]')}`}>
          {/* {t('common.lead')} {leadIndex + 1} */}
          {_.isInteger(_.get(selectedLineItemResult, '[0].array_index', null)) && `${t('common.subBoard')}${_.get(selectedLineItemResult, '[0].array_index', '') + 1}.`}
          _lead
        </span>
      </div>
    </div>
  );
};

export default LeadSegmentRecordCard;
