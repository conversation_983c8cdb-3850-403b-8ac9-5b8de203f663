import React, { Fragment, useEffect, useRef, useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { GreenDefaultButtonConfigProvider, GreenPrimaryButtonConfigProvider, CustomModal } from '../../../../common/styledComponent';
import { Button, Collapse, ConfigProvider, Dropdown, Input, Modal, Tabs, Tooltip, Upload, Select } from 'antd';
import Display from './Display';
import AIDetectROI from '../../../../modal/AIDetectROI';
import ComponentDetail from './ComponentDetail';
import _ from 'lodash';
import { useAddComponentMutation, useAddFeatureMutation, useAddFeatureRoiMutation, useComponentTemplateMapMutation, useDeleteComponentMutation, useDeleteFeatureMutation, useGetAllFeaturesQuery, useGetArrayRegisterationQuery, useGetDatasetGroupAggregatesQuery, useGetProductComponentQuery, useLazyGetAllFeaturesQuery, useLazyGetFeatureByFeatureIdQuery, useLazyGetProductComponentQuery, useLazyGetSampleComponentQuery, useShouldUpdateModelsMutation, useUpdateAgentParamsMutation, useUpdateComponentMutation } from '../../../../services/product';
import { useNavigate } from 'react-router-dom';
import AddFromLibrary from '../../../../modal/AddFromLibrary';
import { getComponentCenterByRoiDtoObj, getComponentRectInfoByFeatures, getFabricViewportCenter, initSolderDefaultLineItemParams, removeProfileHeightFromPayload } from '../../../../viewer/util';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import { addFeatureInGroup, componentRoiPadding, isAOI3DSMT, modelTypes, newRectStrokeWidth, solderFeatureType, solderInspection3D, templateEditorLocateRectWaitTime, updateFeatureInGroup } from '../../../../common/const';
import { useDispatch, useSelector } from 'react-redux';
import { setContainerLvlLoadingMsg, setCurTrainingTaskStartTime, setGlobalRetrainInfo, setIsContainerLvlLoadingEnabled, setIsGlobalRetrainReminderOpened, setIsTrainingRunning, setTemplateEditorShouldRefetchNotifier } from '../../../../reducer/setting';
import { AimOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import DrawModeCol from './DrawModeCol';
import TemplateEditorDrawModeViewer from '../../../../viewer/TemplateEditorDrawModeViewer';
import { systemApi } from '../../../../services/system';
import { useGetReevaluationResultQuery, useModelUpdateTriggerMutation, useReevaluateExamplesMutation } from '../../../../services/inference';
import { getFeatureTypeByLineItemName, sleep } from '../../../../common/util';
import GroupByComponent from './componentList/GroupByComponent';
import GroupByPartNo from './componentList/GroupByPartNo';
import GroupByPackageNo from './componentList/GroupByPackageNo';
import UngroupedFeature from './componentList/UngroupedFeature';
import TemplateEditorRefineInspectionRegion from '../../../../viewer/TemplateEditorRefineInspectionRegionViewer';
import ComponentStatus from './ComponentStatus';


const TemplateEditor = (props) => {
  const {
    curProduct,
    productId,
    refetchCurProduct,
    isRedefiningInspectionRegion,
    setIsRedefiningInspectionRegion,
    isDrawModeEnabled,
    setIsDrawModeEnabled,
  } = props;

  const dispatch = useDispatch();

  const navigate = useNavigate();

  const { t } = useTranslation();

  const fcanvasRef = useRef(null);
  const allFeaturesRef = useRef([]);
  const allComponentsRef = useRef([]);
  const allComponentsCollapseRef = useRef(null);
  const allPartsCollapseRef = useRef(null);
  const allPackagesCollapseRef = useRef(null);
  const allUngroupedFeaturesColumnRef = useRef(null);

  const [isAIDetectROIOpened, setIsAIDetectROIOpened] = useState(false);
  const [isAddFromLibraryOpened, setIsAddFromLibraryOpened] = useState(false);
  const [selectedFid, setSelectedFid] = useState(null); // feature id
  const [selectedCid, setSelectedCid] = useState(null); // component id
  const [selectedArrayIndex, setSelectedArrayIndex] = useState(null); // components' array index
  const [selectedFeatureType, setSelectedFeatureType] = useState(null); // feature type used when group mode is component
  const [selectedPartNo, setSelectedPartNo] = useState(null); // part no used when group mode is part
  const [selectedPackageNo, setSelectedPackageNo] = useState(null); // package no used when group mode is package
  const [selectedScope, setSelectedScope] = useState(null); // component, part, package
  const [selectedUngroupedFid, setSelectedUngroupedFid] = useState(null); // ungrouped feature id
  const [selectedLineItemName, setSelectedLineItemName] = useState(null);
  const [selectedAgentParam, setSelectedAgentParam] = useState(null); // `${agent name}.${agent param name}`
  const [currentNewTemplateFromLibrary, setCurrentNewTemplateFromLibrary] = useState(null);
  const [requiredLocateRect, setRequiredLocateRect] = useState({
    cid: null,
    fid: null,
  });
  const [hoveredCid, setHoveredCid] = useState(null);
  const [hoveredFid, setHoveredFid] = useState(null);
  const [hoveredLineItemName, setHoveredLineItemName] = useState(null);
  // const [isDrawModeEnabled, setIsDrawModeEnabled] = useState(false);
  const [componentQuery, setComponentQuery] = useState('');
  const [componentSearchValues, setComponentSearchValues] = useState([]);
  const [healthyFilter, setHealthyFilter] = useState(undefined);
  const [reevaluationPassFilter, setReevaluationPassFilter] = useState(undefined);
  const [filteredComponents, setFilteredComponents] = useState([]);
  const [shouldUpdateModelButtonType, setShouldUpdateModelButtonType] = useState(false);
  const [trainingSetSelectedDetail, setTrainingSetSelectedDetail] = useState(null);
  const [trainingSetSelectedErrorType, setTrainingSetSelectedErrorType] = useState(null);
  const [componentReevaluationResultMap, setComponentReevaluationResultMap] = useState(null);
  const [allFeatures, setAllFeatures] = useState([]); // all features of the current product
  // const [allComponents, setAllComponents] = useState([]); // all components of the current product
  const [updatedFeatures, setUpdatedFeatures] = useState([]);
  const [mmToPixelRatio, setMmToPixelRatio] = useState(null); // mm to pixel ratio for the current product
  const [componentListGroupMode, setComponentListGroupMode] = useState('component'); // component, part, package
  const [emptyComponents, setEmptyComponents] = useState([]);
  const [ungroupedFeatures, setUngroupedFeatures] = useState([]);
  const [autoGenAgentParamListeningFinished, setAutoGenAgentParamListeningFinished] = useState(false);
  const [isPesudoColorDisplayed, setIsPesudoColorDisplayed] = useState(false);
  const [shouldTrainingSetRefetch, setShouldTrainingSetRefetch] = useState(false);
  const [componentDetailWidth, setComponentDetailWidth] = useState(388);
  const [isResizingComponentDetail, setIsResizingComponentDetail] = useState(false);
  const [minComponentDetailWidth] = useState(280);
  const [maxComponentDetailWidth] = useState(600);
  const [isUnhealthyModalVisible, setIsUnhealthyModalVisible] = useState(false);
  const [hasShownUnhealthyModal, setHasShownUnhealthyModal] = useState(false);

  const templateEditorShouldRefetchNotifier = useSelector(state => state.setting.templateEditorShouldRefetchNotifier);

  const componentCount = filteredComponents?.length || 0;

  const partCount = useMemo(() => {
    const keys = new Set();
    for (const c of filteredComponents || []) {
      if (!_.isEmpty(c.part_no) && c.can_group_by_part_no === true) {
        keys.add(`part:${c.part_no}`);
      } else {
        keys.add(`cid:${c.region_group_id}`);
      }
    }
    return keys.size;
  }, [filteredComponents]);

  const packageCount = useMemo(() => {
    const keys = new Set();
    for (const c of filteredComponents || []) {
      if (!_.isEmpty(c.package_no)) {
        if (c.can_group_by_package_no === true && c.can_group_by_part_no === true) {
          keys.add(`package:${c.package_no}`);
        } else if (c.can_group_by_part_no === false && c.can_group_by_package_no === false) {
          keys.add(`cid:${c.region_group_id}`);
        } else if (c.can_group_by_part_no !== true) {
          if (_.isEmpty(c.part_no)) {
            keys.add(`package:${c.package_no}`);
          } else {
            keys.add(`cid:${c.region_group_id}`);
          }
        } else if (c.can_group_by_package_no === false) {
          if (_.isEmpty(c.part_no)) {
            keys.add(`cid:${c.region_group_id}`);
          } else {
            keys.add(`part:${c.package_no}/${c.part_no}`);
          }
        } else {
          keys.add(`package:${c.package_no}`);
        }
      } else {
        if (!_.isEmpty(c.part_no) && c.can_group_by_part_no === true) {
          keys.add(`part_unknown:${c.part_no}`);
        } else {
          keys.add(`cid:${c.region_group_id}`);
        }
      }
    }
    return keys.size;
  }, [filteredComponents]);

  // const { data: allFeatures, refetch: refetchAllFeatures } = useGetAllFeaturesQuery({ product_id: productId, step: 0, variant: _.get(curProduct, 'product_name', ''), marker: false });
  // const { data: arrayRegisteration, refetch: refetchArrayRegisteration } = useGetArrayRegisterationQuery({ product_id: productId, step: 0 });
  const componentFetchParams = useMemo(() => {
    const params = { definition_product_id: productId, definition_step: 0 };
    if (_.isBoolean(healthyFilter)) params.healthy = healthyFilter;
    if (_.isBoolean(reevaluationPassFilter)) params.reevaluation_pass = reevaluationPassFilter;
    return params;
  }, [productId, healthyFilter, reevaluationPassFilter]);
  const { data: allComponents, refetch: refetchAllComponents } = useGetProductComponentQuery(componentFetchParams);
  const { data: allFeatureReevaluationResult, refetch: refetchAllFeatureReevaluationResult } = useGetReevaluationResultQuery({
    golden_product_id:
    productId, step: 0,
    has_feedback: true
  });
  const { data: aggregatedReevaluationResult, refetch: refetchAggregatedReevaluationResult } = useGetDatasetGroupAggregatesQuery({
    golden_product_id: productId,
    step: 0,
    group_level: _.upperCase(componentListGroupMode),
  });
  const [physicalCoordMap] = useComponentTemplateMapMutation();
  const [addFeature] = useAddFeatureMutation();
  const [addComponent] = useAddComponentMutation();
  const [updateComponent] = useUpdateComponentMutation();
  const [deleteComponent] = useDeleteComponentMutation();
  const [deleteFeature] = useDeleteFeatureMutation();
  const [retrainTrigger] = useModelUpdateTriggerMutation();
  const [reevaluateExamples] = useReevaluateExamplesMutation();
  const [shouldUpdateModels] = useShouldUpdateModelsMutation();
  const [lazyGetAllFeatures] = useLazyGetAllFeaturesQuery();
  const [lazyGetFeatureById] = useLazyGetFeatureByFeatureIdQuery();
  const [lazyGetProductComponent] = useLazyGetProductComponentQuery();
  const [updateAgentParams] = useUpdateAgentParamsMutation();
  const [lazyGetSampleComponent] = useLazyGetSampleComponentQuery();
  const [addFeatureRoi] = useAddFeatureRoiMutation();

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));
  const isTrainingRunning = useSelector(state => state.setting.isTrainingRunning);

  const handleComponentDetailMouseDown = (e) => {
    setIsResizingComponentDetail(true);
    e.preventDefault();
  };

  const handleComponentDetailMouseMove = React.useCallback((e) => {
    if (!isResizingComponentDetail) return;

    const newWidth = window.innerWidth - e.clientX;

    if (newWidth >= minComponentDetailWidth && newWidth <= maxComponentDetailWidth) {
      setComponentDetailWidth(newWidth);
    }
  }, [isResizingComponentDetail, minComponentDetailWidth, maxComponentDetailWidth]);

  const handleComponentDetailMouseUp = React.useCallback(() => {
    setIsResizingComponentDetail(false);
  }, []);

  const handleComponentSearchChange = (values) => {
    const addedValues = values.filter(v => !componentSearchValues.includes(v));

    const updatedValues = [...new Set(values)];

    if (updatedValues.includes('healthy') && updatedValues.includes('unhealthy')) {
      updatedValues.splice(updatedValues.indexOf(_.includes(addedValues, 'unhealthy') ? 'healthy' : 'unhealthy'), 1);
    }

    if (updatedValues.includes('reevaluation_pass') && updatedValues.includes('reevaluation_fail')) {
      updatedValues.splice(updatedValues.indexOf(_.includes(addedValues, 'reevaluation_fail') ? 'reevaluation_pass' : 'reevaluation_fail'), 1);
    }

    setComponentSearchValues(updatedValues);
    setHealthyFilter(updatedValues.includes('healthy')
      ? true
      : updatedValues.includes('unhealthy')
        ? false
        : undefined);
    setReevaluationPassFilter(updatedValues.includes('reevaluation_pass')
      ? true
      : updatedValues.includes('reevaluation_fail')
        ? false
        : undefined);
    const tokens = updatedValues.filter(v => !['healthy', 'unhealthy', 'reevaluation_pass', 'reevaluation_fail'].includes(v));
    setComponentQuery(tokens.join(' '));

    refetchAllComponents();
  };

  useEffect(() => {
    if (isResizingComponentDetail) {
      document.addEventListener('mousemove', handleComponentDetailMouseMove);
      document.addEventListener('mouseup', handleComponentDetailMouseUp);
    } else {
      document.removeEventListener('mousemove', handleComponentDetailMouseMove);
      document.removeEventListener('mouseup', handleComponentDetailMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleComponentDetailMouseMove);
      document.removeEventListener('mouseup', handleComponentDetailMouseUp);
    };
  }, [isResizingComponentDetail, handleComponentDetailMouseMove, handleComponentDetailMouseUp]);

  const handleAutoGenerateAgentParams = async (
    productId,
  ) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.autoGeneratingAgentParams')));

    const res = await retrainTrigger({
      model_types: [
        modelTypes.mounting3DModel,
        modelTypes.lead3DModel,
        modelTypes.solder3DModel,
        modelTypes.lead2DV2Model,
        modelTypes.solder2DModel,
      ],
      golden_product_id: Number(productId),
    });

    if (res.error) {
      aoiAlert(t('notification.error.autoGenerateAgentParams'), ALERT_TYPES.COMMON_ERROR);
      console.error('retrainTrigger error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    setAutoGenAgentParamListeningFinished(true);

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.refetchingFeatures')));
    dispatch(setIsTrainingRunning(true));
    dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
  };

  const refetchAllFeatures = async () => {
    const res = await lazyGetAllFeatures({ product_id: productId, step: 0, variant: _.get(curProduct, 'product_name', ''), marker: false });

    if (res.error) {
      console.error('getAllFeatures error:', _.get(res, 'error.message', ''));
      aoiAlert(t('notification.error.getAllFeatures'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    setAllFeatures(res.data);
  };

  // const refetchAllComponents = async () => {
  //   const res = await lazyGetProductComponent({ definition_product_id: productId, definition_step: 0 });

  //   if (res.error) {
  //     console.error('getProductComponent error:', _.get(res, 'error.message', ''));
  //     aoiAlert(t('notification.error.getAllComponents'), ALERT_TYPES.COMMON_ERROR);
  //     return;
  //   }

  //   setAllComponents(res.data);
  // };

  const updateAllFeaturesState = async (featureIds, action, newfeatureObjs) => {
    let newAllFeatures;

    let featureObjs = [];
    if (action !== 'delete') {
      if (!newfeatureObjs) {
        for (const featureId of featureIds) {
          const res = await lazyGetFeatureById({ product_id: productId, step: 0, feature_id: featureId });

          if (res.error) {
            console.error('getFeatureById error:', _.get(res, 'error.message', ''));
            aoiAlert(t('notification.error.getFeatureByFeatureId'), ALERT_TYPES.COMMON_ERROR);
            return;
          }

          featureObjs.push(res.data);
        }
      } else {
        featureObjs = newfeatureObjs;
      }
    }

    if (action === 'update') {
      if (!newfeatureObjs) {
        newAllFeatures = _.map(allFeaturesRef.current, f => {
          if (_.some(featureIds, id => _.get(f, 'feature_id', 0) === id)) {
            const featureObj = _.find(featureObjs, o => _.get(o, 'feature_id', 0) === _.get(f, 'feature_id', 0));
            if (featureObj) {
              return featureObj;
            }
          }
          return f;
        });
      } else {
        newAllFeatures = _.map(allFeaturesRef.current, f => {
          if (_.find(newfeatureObjs, newF => newF.feature_id === f.feature_id && newF.array_index === f.array_index)) {
            return _.find(newfeatureObjs, o => _.get(o, 'feature_id', 0) === _.get(f, 'feature_id', 0) && _.get(o, 'array_index', null) === _.get(f, 'array_index', null));
          }
          return f;
        });
      }
    } else if (action === 'add') {
      if (!_.isEmpty(allFeaturesRef.current)) {
        newAllFeatures = [...allFeaturesRef.current, ...featureObjs];
      } else {
        newAllFeatures = featureObjs;
      }
    } else if (action === 'delete') {
      newAllFeatures = _.filter(allFeaturesRef.current, f => !_.some(featureIds, id => _.get(f, 'feature_id', 0) === id));
    } else if (action === 'updateGroupParam') {
      // we expect the new feature objs are the new line item params
      // and featureIds are the target features
      const fidSet = new Set(featureIds);

      newAllFeatures = _.map(allFeaturesRef.current, f => {
        if (fidSet.has(f.feature_id)) {
          return {
            ...f,
            line_item_params: newfeatureObjs,
          };
        }
        return f;
      });
    } else if (action === updateFeatureInGroup) {
      // we expect the new feature objs are the new feature dto but without line item params
      // and featureIds are the target features
      const fidSet = new Set(featureIds);
      newAllFeatures = _.map(allFeaturesRef.current, f => {
        if (fidSet.has(f.feature_id) && _.find(newfeatureObjs, o => o.feature_id === f.feature_id && o.array_index === f.array_index)) {
          // NOTE: line_item_params will exist(null) so we need remove it from the new feature obj to avoid overwriting it
          return {
            ...f,
            ..._.find(newfeatureObjs, o => o.feature_id === f.feature_id && o.array_index === f.array_index),
            line_item_params: f.line_item_params,
          };
        }
        return f;
      });
    } else if (action === addFeatureInGroup) {
      newAllFeatures = [...allFeaturesRef.current, ...newfeatureObjs];
    }

    setAllFeatures(newAllFeatures);
    allFeaturesRef.current = newAllFeatures;

    // also refetch the re-evaluation result
    await refetchAllFeatureReevaluationResult();
    await refetchAggregatedReevaluationResult();

    // setUpdatedFeatures({
    //   action,
    //   newfeatureObjs,
    // });
  };

  const updateAllComponentsState = async (action, newComponentObjs) => {
    let newAllComponents;

    if (action === 'update') {
      newAllComponents = _.map(allComponentsRef.current, c => {
        if (_.find(newComponentObjs, newC => newC.region_group_id === c.region_group_id)) {
          return _.find(newComponentObjs, o => o.region_group_id === c.region_group_id);
        }
        return c;
      });
    } else if (action === 'add') {
      if (!_.isEmpty(allComponentsRef.current)) {
        newAllComponents = [...allComponentsRef.current, ...newComponentObjs];
      } else {
        newAllComponents = newComponentObjs;
      }
    } else if (action === 'delete') {
      newAllComponents = _.filter(allComponentsRef.current, c => !_.some(newComponentObjs, obj => obj.region_group_id === c.region_group_id));
    }

    setAllComponents(newAllComponents);
    allComponentsRef.current = newAllComponents;
  };

  useEffect(() => {
    refetchAllFeatureReevaluationResult();
    refetchAggregatedReevaluationResult();
  }, [componentListGroupMode]);

  useEffect(() => {
    if (!autoGenAgentParamListeningFinished || isTrainingRunning) return;

    const refetch = async () => {
      await refetchAllFeatures();
      await refetchAllComponents();
      await refetchAllFeatureReevaluationResult();
      await refetchAggregatedReevaluationResult();

      setAutoGenAgentParamListeningFinished(false);
    };

    refetch();
  }, [
    autoGenAgentParamListeningFinished,
    isTrainingRunning,
  ]);

  useEffect(() => {
    if (_.isEmpty(allFeatures)) return;

    const checkShouldUpdateModels = async (productId) => {
      const res = await shouldUpdateModels({
        model_types: [
          modelTypes.mountingModel,
          modelTypes.leadModel
        ],
        golden_product_id: Number(productId),
      });

      setShouldUpdateModelButtonType(_.some(res.data, d => d));
    }

    checkShouldUpdateModels(productId);
    setShouldTrainingSetRefetch(true);
  }, [allFeatures]);

  useEffect(() => {
    if (!_.isEmpty(allComponents)) {
      // 检查是否有不健康的组件
      const hasUnhealthyComponents = _.some(allComponents, component =>
        _.get(component, 'healthy', true) === false
      );

      // 如果有不健康组件且还没有显示过modal，则显示modal
      if (hasUnhealthyComponents && !hasShownUnhealthyModal) {
        setIsUnhealthyModalVisible(true);
        setHasShownUnhealthyModal(true);
      }

      if (_.isEmpty(componentQuery)) {
        setFilteredComponents(allComponents);
        // console.log('Filtered components reset to all components', allComponents);

        return;
      }
      const tempFilteredComponents = _.filter(allComponents, c => {
        return _.includes(_.get(c, 'package_no', ''), componentQuery) ||
        _.includes(_.get(c, 'region_group_id', ''), componentQuery) ||
        _.includes(_.get(c, 'designator', ''), componentQuery) ||
        _.includes(_.get(c, 'part_no', ''), componentQuery);
      })



      setFilteredComponents(tempFilteredComponents);
      // console.log('Filtered components updated based on query', componentQuery, filteredComponents);

    } else {
      setFilteredComponents([]);
    }
  }, [allComponents, componentQuery]);

  useEffect(() => {
    if (_.isEmpty(currentNewTemplateFromLibrary)) return;
    if (!fcanvasRef.current) return;

    const curSelectedComponent = _.find(allComponents, c => c.region_group_id === selectedCid);

    const run = async (currentNewTemplateFromLibrary, curProduct, curSelectedComponent, allFeatures, systemMetadata, allComponents) => {
      dispatch(setIsContainerLvlLoadingEnabled(true));
      dispatch(setContainerLvlLoadingMsg(t('loader.addingNewComponent')));

      const isPrivateTemplate = !_.get(currentNewTemplateFromLibrary, 'summary.builtin', true);

      // get vp center
      let vpCenter = null;

      if (!curSelectedComponent || _.isUndefined(curSelectedComponent)) {
        // triggered from add from library
        // get current viewer's viewport center
        vpCenter = getFabricViewportCenter(fcanvasRef.current);
      } else {
        // triggered from replace component with template
        // get component's center
        vpCenter = getComponentCenterByRoiDtoObj(_.get(curSelectedComponent, 'shape', {}));
      }

      // either add from new component or replace existing component with template we need to calculate the new component's shape first
      // get sample component
      let getSampleComponentPayload = {
        definition_product_id: Number(_.get(curProduct, 'product_id', 0)),
        definition_step: 0,
      };

      if (_.get(currentNewTemplateFromLibrary, 'summary.package_no', '')) {
        getSampleComponentPayload.package_no = _.get(currentNewTemplateFromLibrary, 'summary.package_no', '');
      } else if (_.get(currentNewTemplateFromLibrary, 'summary.part_no', '')) {
        getSampleComponentPayload.part_no = _.get(currentNewTemplateFromLibrary, 'summary.part_no', '');
      }

      const sampleComponentRes = await lazyGetSampleComponent(getSampleComponentPayload);

      if (sampleComponentRes.error) {
        console.error('getSampleComponent error:', _.get(sampleComponentRes, 'error.message', ''));
        aoiAlert(t('notification.error.getComponent'), ALERT_TYPES.COMMON_ERROR);
        dispatch(setIsContainerLvlLoadingEnabled(false));
        dispatch(setContainerLvlLoadingMsg(''));
        return;
      }

      let curComponentPMinX = Infinity;
      let curComponentPMaxX = -Infinity;
      let curComponentPMinY = Infinity;
      let curComponentPMaxY = -Infinity;

      const sampleCid = _.get(sampleComponentRes, 'data.component_id', null);

      const newFeaturePayloads = [];

      let sampleFeatures = [];

      if (_.isInteger(sampleCid)) {
        // calc new component shape by sample component's features
        const sampleComponent = _.find(allComponents, c => c.region_group_id === sampleCid);

        if (!sampleComponent) return;

        sampleFeatures = _.filter(allFeatures, f => f.group_id === sampleCid);

        if (_.isEmpty(sampleFeatures)) return;

        const newComponentInfo = getComponentRectInfoByFeatures(sampleFeatures);

        curComponentPMinX = newComponentInfo.pMin.x;
        curComponentPMaxX = newComponentInfo.pMax.x;
        curComponentPMinY = newComponentInfo.pMin.y;
        curComponentPMaxY = newComponentInfo.pMax.y;

        // also need to generate the new feature payloads
        let sourceCenter;
        if (curSelectedComponent) {
          sourceCenter = getComponentCenterByRoiDtoObj(_.get(curSelectedComponent, 'shape', {}));
        } else {
          sourceCenter = vpCenter;
        }

        // basically the sample component and features in source component's position with source component's rotation
        // get translation and rotation delta
        const sampleCenter = getComponentCenterByRoiDtoObj(_.get(sampleComponent, 'shape', {}));

        // we need sample feature position + delta = the feature in source component's position
        const translationDelta = {
          x: sourceCenter.x - sampleCenter.x,
          y: sourceCenter.y - sampleCenter.y,
        };

        // apply translation to component pmin pmax
        curComponentPMinX += translationDelta.x;
        curComponentPMaxX += translationDelta.x;
        curComponentPMinY += translationDelta.y;
        curComponentPMaxY += translationDelta.y;

        for (const f of sampleFeatures) {
          // apply some rotation
          let fCenter = getComponentCenterByRoiDtoObj(_.get(f, 'roi', {}));
          // rotate around sample component's center
          fCenter = rotatePoint(fCenter, -_.get(sampleComponent, 'shape.angle', 0), sampleCenter);
          // apply translation
          fCenter = {
            x: fCenter.x + translationDelta.x,
            y: fCenter.y + translationDelta.y,
          };
          // get pmin pmax
          const fPMinPMax = {
            pMin: {
              x: _.round(fCenter.x - (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2, 0),
              y: _.round(fCenter.y - (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2, 0),
            },
            pMax: {
              x: _.round(fCenter.x + (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2 - 1, 0),
              y: _.round(fCenter.y + (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2 - 1, 0),
            },
          };

          newFeaturePayloads.push({
            ...f,
            roi: {
              ...f.roi,
              points: [
                fPMinPMax.pMin,
                fPMinPMax.pMax,
              ],
              angle: _.get(f, 'roi.angle', 0),
              center: null,
            },
            group_id: curSelectedComponent.region_group_id, // orverride group_id to the selected component's group_id
          });
        }
      } else {
        // not found then calc new component shape by template's features
        // convert the selected template roi from physical to image coordinate(at the center of the current viewport)
        const res = await physicalCoordMap({
          depth_map_uri: _.get(curProduct, 'inspectables[0].depth_map_uri', ''),
          u: _.floor(isPrivateTemplate ? 0 : vpCenter.x, 0),
          v: _.floor(isPrivateTemplate ? 0 : vpCenter.y, 0),
          canned_rois: _.get(currentNewTemplateFromLibrary, 'model.canned_rois', []),
        });

        if (res.error) {
          console.error('physicalCoordMapping error:', _.get(res, 'error.message', ''));
          aoiAlert(t('notification.error.mapComponentTemplateCoord'), ALERT_TYPES.COMMON_ERROR);
          dispatch(setIsContainerLvlLoadingEnabled(false));
          dispatch(setContainerLvlLoadingMsg(''));
          return;
        }

        let translationDelta = { x: 0, y: 0 };
        const parsedForCenter = _.map(_.get(res, 'data', []), f => ({ ...f, roi: f.shape }));
        const tmpInfo = getComponentRectInfoByFeatures(parsedForCenter);
        if (isPrivateTemplate) {
          const center = {
            x: tmpInfo.pMin.x + (tmpInfo.pMax.x - tmpInfo.pMin.x) / 2,
            y: tmpInfo.pMin.y + (tmpInfo.pMax.y - tmpInfo.pMin.y) / 2,
          };
          translationDelta = { x: vpCenter.x - center.x, y: vpCenter.y - center.y };
        }

        // generate new feature requests' payloads
        for (const i of _.range(_.get(res, 'data', []).length)) {
          let payload = {
            // variant: _.get(_.first(_.get(curProduct, 'inspectables', [])), 'variant', ''),
            step: 0,
            product_id: Number(_.get(curProduct, 'product_id', 0)),
            roi: {
              type: 'obb',
              points: [
                {
                  x: _.round(_.get(res, `data[${i}]shape.points[0].x`, 0) + translationDelta.x, 0),
                  y: _.round(_.get(res, `data[${i}]shape.points[0].y`, 0) + translationDelta.y, 0),
                },
                {
                  x: _.round(_.get(res, `data[${i}]shape.points[1].x`, 0) + translationDelta.x, 0),
                  y: _.round(_.get(res, `data[${i}]shape.points[1].y`, 0) + translationDelta.y, 0),
                },
              ],
              angle: _.get(res, `data[${i}]shape.angle`, 0),
              center: null
            },
            feature_type: getFeatureTypeByLineItemName(_.get(currentNewTemplateFromLibrary, `model.canned_rois[${i}].checklist`)),
            feature_scope: 'product',
            line_item_params: _.get(currentNewTemplateFromLibrary, `model.canned_rois[${i}].checklist`)
          };

          if (payload.feature_type === solderFeatureType) {
            const rectDimension = {
              width: _.get(res, `data[${i}]shape.points[1].x`, 0) - _.get(res, `data[${i}]shape.points[0].x`, 0),
              height: _.get(res, `data[${i}]shape.points[1].y`, 0) - _.get(res, `data[${i}]shape.points[0].y`, 0),
            };

            const solderDefaultParams = initSolderDefaultLineItemParams(_.get(systemMetadata, 'default_line_items', {}));

            if (isAOI3DSMT) {
              if (rectDimension.width >= rectDimension.height) {
                payload = {
                  ...payload,
                  line_item_params: {
                    [solderInspection3D]: {
                      ...solderDefaultParams[solderInspection3D],
                      params: {
                        ...solderDefaultParams[solderInspection3D].params,
                        // profile_height: {
                        //   ...solderDefaultParams[solderInspection3D].params.profile_height,
                        //   param_int: {
                        //     ...solderDefaultParams[solderInspection3D].params.profile_height.param_int,
                        //     value: _.floor(payload.roi.points[1].y - payload.roi.points[0].y + 1 +
                        //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_bottom.param_int.value`, 0) +
                        //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_top.param_int.value`, 0), 0),
                        //   },
                        // },
                        profile_width: {
                          ...solderDefaultParams[solderInspection3D].params.profile_width,
                          param_int: {
                            ...solderDefaultParams[solderInspection3D].params.profile_width.param_int,
                            value: _.round((rectDimension.width - newRectStrokeWidth - 1)/2, 0),
                          },
                        },
                      }
                    }
                  },
                };
              } else {
                payload = {
                  ...payload,
                  line_item_params: {
                    [solderInspection3D]: {
                      ...solderDefaultParams[solderInspection3D],
                      params: {
                        ...solderDefaultParams[solderInspection3D].params,
                        // profile_height: {
                        //   ...solderDefaultParams[solderInspection3D].params.profile_height,
                        //   param_int: {
                        //     ...solderDefaultParams[solderInspection3D].params.profile_height.param_int,
                        //     value: _.floor(roi.points[1].y - roi.points[0].y + 1 +
                        //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_bottom.param_int.value`, 0) +
                        //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_top.param_int.value`, 0), 0),
                        //   },
                        // },
                        profile_width: {
                          ...solderDefaultParams[solderInspection3D].params.profile_width,
                          param_int: {
                            ...solderDefaultParams[solderInspection3D].params.profile_width.param_int,
                            value: _.round((curDrawingRectRef.current.width - newRectStrokeWidth - 1)/2, 0),
                          },
                        },
                      }
                    }
                  },
                };
              }
            }
          }

          newFeaturePayloads.push(payload);
        }

        const parsedFeatures = _.map(_.get(res, 'data', []), f => ({
          ...f,
          roi: {
            ...f.shape,
            points: [
              {
                x: _.get(f, 'shape.points[0].x', 0) + translationDelta.x,
                y: _.get(f, 'shape.points[0].y', 0) + translationDelta.y,
              },
              {
                x: _.get(f, 'shape.points[1].x', 0) + translationDelta.x,
                y: _.get(f, 'shape.points[1].y', 0) + translationDelta.y,
              },
            ],
          },
        }));
        const newComponentInfo = getComponentRectInfoByFeatures(parsedFeatures);

        curComponentPMinX = newComponentInfo.pMin.x;
        curComponentPMaxX = newComponentInfo.pMax.x;
        curComponentPMinY = newComponentInfo.pMin.y;
        curComponentPMaxY = newComponentInfo.pMax.y;
      }

      const newFeatureIds = [];
      const newFeatureObjs = [];

      if (!curSelectedComponent || _.isUndefined(curSelectedComponent)) {
        // if add new from library
        // 1. calculate the new component's shape based on template's canned rois or existed group's shape(which is done previously)
        // 2. call add new component
        // if the part/package no from template matches any existing group then we should use the existing's group info
        // else we info from template

        const payload = {
          shape: {
            type: 'obb',
            center: {
              x: vpCenter.x,
              y: vpCenter.y,
            },
            points: [
              {
                x: curComponentPMinX,
                y: curComponentPMinY,
              },
              {
                x: curComponentPMaxX,
                y: curComponentPMaxY,
              }
            ],
            angle: 0,
          },
          definition_product_id: Number(_.get(curProduct, 'product_id', 0)),
          definition_step: 0,
          designator: `${_.get(currentNewTemplateFromLibrary, 'summary.package_no', '')}-${_.get(curProduct, 'product_id', '')}`,
          center: {
            x: _.round(vpCenter.x, 0),
            y: _.round(vpCenter.y, 0),
          },
        };

        if (!_.isEmpty(_.get(currentNewTemplateFromLibrary, 'summary.package_no', ''))) {
          payload.package_no = _.get(currentNewTemplateFromLibrary, 'summary.package_no', '');
        }
        
        if (!_.isEmpty(_.get(currentNewTemplateFromLibrary, 'summary.part_no', ''))) {
          payload.part_no = _.get(currentNewTemplateFromLibrary, 'summary.part_no', '');
        }

        const addComponentRes = await addComponent(payload);

        if (addComponentRes.error) {
          aoiAlert(t('notification.error.addComponent'), ALERT_TYPES.COMMON_ERROR);
          console.error('addComponent error:', _.get(addComponentRes, 'error.message', ''));
          dispatch(setIsContainerLvlLoadingEnabled(false));
          dispatch(setContainerLvlLoadingMsg(''));
          return;
        }

        await refetchAllComponents();

        if (!_.get(addComponentRes, 'data.cloned', false)) {
          const newFeatures = [];
          // not cloned means the group from template is not found in the existing groups
          for (const f of newFeaturePayloads) {
            const addFeatureRes = await addFeatureRoi({
              body: removeProfileHeightFromPayload({
                ...f,
                group_id: _.get(addComponentRes, 'data.components[0].region_group_id', 0),
              }),
              params: { allComponents: true },
            });

            newFeatures.push(...addFeatureRes.data);
          }

          await updateAllFeaturesState(_.map(newFeatures, 'feature_id'), 'add', newFeatures);
        } else {
          const resAllFeatures = await lazyGetAllFeatures({
            product_id: Number(_.get(curProduct, 'product_id', 0)),
            step: 0,
            variant: _.get(curProduct, 'product_name', ''),
            marker: false,
            component_id: _.get(addComponentRes, 'data.components[0].region_group_id', 0),
          });

          if (resAllFeatures.error) {
            console.error('getAllFeatures error:', _.get(resAllFeatures, 'error.message', ''));
            aoiAlert(t('notification.error.getAllFeatures'), ALERT_TYPES.COMMON_ERROR);
            return;
          }

          await updateAllFeaturesState(_.map(resAllFeatures.data, 'feature_id'), 'add', resAllFeatures.data);
        }
      } else {
        // 0. calc new component and features shape(which is done previously)
        // 1. check if part/package no from template matches any existing group
        // 2. if exists then get all features in the sample component id
        // 3. delete features in selected component(group level action)
        // 4. add new features in selected component(group level action)
        // 5. update component with all: true(group level action)

        // delete old features
        const relatedOldFeatures = _.filter(allFeatures, f => f.group_id === curSelectedComponent.region_group_id);

        if (!_.isEmpty(relatedOldFeatures)) {
          for (const f of relatedOldFeatures) {
            await deleteFeature({
              product_id: Number(_.get(curProduct, 'product_id', 0)),
              step: 0,
              feature_id: _.get(f, 'feature_id', 0),
              variant: _.get(f, 'variant'),
            });
          }

          await updateAllFeaturesState(_.map(relatedOldFeatures, 'feature_id'), 'delete', relatedOldFeatures);
        }

        // add new features
        for (const payload of newFeaturePayloads) {
          const addFeatureRes = await addFeatureRoi({
            body: removeProfileHeightFromPayload({
              ...payload,
              group_id: curSelectedComponent.region_group_id,
            }),
            params: { allComponents: true },
          });

          if (addFeatureRes.error) {
            console.error('addFeature error:', _.get(addFeatureRes, 'error.message', ''));
            aoiAlert(t('notification.error.addFeature'), ALERT_TYPES.COMMON_ERROR);
            dispatch(setIsContainerLvlLoadingEnabled(false));
            dispatch(setContainerLvlLoadingMsg(''));
            return;
          }

          newFeatureIds.push(..._.map(addFeatureRes.data, 'feature_id'));
          newFeatureObjs.push(..._.map(addFeatureRes.data, f => ({
            ...f,
            line_item_params: payload.line_item_params,
          })));
        }

        const updateComponentPayload = {
          definition_product_id: Number(_.get(curProduct, 'product_id', 0)),
          definition_step: 0,
          region_group_id: curSelectedComponent.region_group_id,
          designator: curSelectedComponent.designator,
          package_no: _.get(currentNewTemplateFromLibrary, 'summary.package_no', ''),
          part_no: _.get(currentNewTemplateFromLibrary, 'summary.part_no', ''),
          center: {
            x: _.round(vpCenter.x, 0),
            y: _.round(vpCenter.y, 0),
          },
          shape: {
            type: 'obb',
            points: [
              {
                x: curComponentPMinX,
                y: curComponentPMinY,
              },
              {
                x: curComponentPMaxX,
                y: curComponentPMaxY,
              }
            ],
            center: null,
            angle: 0,
          },
          // all: true,
        };

        const updateComponentRes = await updateComponent({
          body: updateComponentPayload,
          params: { allComponents: false },
        });

        if (updateComponentRes.error) {
          aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
          console.error('updateComponent error:', _.get(updateComponentRes, 'error.message', ''));
          dispatch(setIsContainerLvlLoadingEnabled(false));
          dispatch(setContainerLvlLoadingMsg(''));
          return;
        }

        await refetchAllComponents();
        await updateAllFeaturesState(newFeatureIds, 'add', newFeatureObjs);
      }

      setCurrentNewTemplateFromLibrary(null);
      setIsAddFromLibraryOpened(false);
    };

    run(currentNewTemplateFromLibrary, curProduct, curSelectedComponent, allFeatures, systemMetadata, allComponents);
  }, [currentNewTemplateFromLibrary]);

  const handleAllFeatureReevaluateTrigger = async (productId) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.reevaluatingAllExamples')));

    const res = await reevaluateExamples({
      golden_product_id: Number(productId),
      step: 0,
      has_feedback: true,
    });

    if (res.error) {
      aoiAlert(t('notification.error.reevaluateExample'), ALERT_TYPES.COMMON_ERROR);
      console.error('reevaluate example failed', res.error.message);
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    aoiAlert(t('notification.success.reevaluateExample'), ALERT_TYPES.COMMON_SUCCESS);

    await refetchAllFeatureReevaluationResult();
    await refetchAggregatedReevaluationResult();

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  };

  useEffect(() => {
    if (_.isEmpty(allFeatureReevaluationResult) || _.isEmpty(allFeatures)) {
      setComponentReevaluationResultMap(null);
      return;
    }

    const newMap = {};
    const newComponentResultMap = {};

    const fidToFeatureMap = _.keyBy(allFeatures, f => _.get(f, 'feature_id', 0));
    const cidToComponentMap = _.keyBy(allComponents, c => _.get(c, 'region_group_id', 0));

    for (const featureInferenceResult of allFeatureReevaluationResult) {
      newMap[_.get(featureInferenceResult, 'feature_id', 0)] = featureInferenceResult;
      const gid = _.get(fidToFeatureMap, `${_.get(featureInferenceResult, 'feature_id', 0)}.group_id`, null);
      if (!gid) continue;
      if (!_.has(cidToComponentMap, `${gid}`)) continue;
      if (_.has(newComponentResultMap, `${gid}`)) {
        newComponentResultMap[gid] = {
          ...newComponentResultMap[gid],
          num_failing: _.get(newComponentResultMap[gid], 'num_failing', 0) + _.get(featureInferenceResult, 'num_failing', 0),
          num_passing: _.get(newComponentResultMap[gid], 'num_passing', 0) + _.get(featureInferenceResult, 'num_passing', 0),
          healthy: newComponentResultMap[gid].healthy && _.get(cidToComponentMap, `${gid}.healthy`, false), // in case other sub board(component from the same group)'s component's healthy is false
        }
      } else {
        newComponentResultMap[gid] = {
          ...featureInferenceResult,
          healthy: _.get(cidToComponentMap, `${gid}.healthy`, false),
        };
      }
      if (_.get(featureInferenceResult, 'num_failing', 0) === 0 && _.get(featureInferenceResult, 'num_passing', 0) === 0) {
        // if no failing and passing, set it to null
        newComponentResultMap[gid] = {
          ...newComponentResultMap[gid],
          contains_white_feature: true,
        };
      }
    }

    setComponentReevaluationResultMap(newComponentResultMap);
  }, [allFeatureReevaluationResult, allFeatures, allComponents]);

  useEffect(() => {
    if (!_.isEmpty(allFeatures)) {
      allFeaturesRef.current = allFeatures;
    } else {
      allFeaturesRef.current = null;
    }
  }, [allFeatures]);

  useEffect(() => {
    if (!_.isEmpty(allComponents)) {
      allComponentsRef.current = allComponents;
    } else {
      allComponentsRef.current = null;
    }
  }, [allComponents]);

  useEffect(() => {
    if (!_.isInteger(selectedCid) && !_.isInteger(selectedUngroupedFid)) return;

    if (!allComponentsCollapseRef.current || !allUngroupedFeaturesColumnRef.current) return;

    const run = async (selectedCid, selectedUngroupedFid) => {
      // scroll to selected collapse item
      if (_.isInteger(selectedCid)) {
        // let expand animation finish before scrolling ow it will scroll to the bottom
        await sleep(330);
        // search for all component collapse elements' children that has ant-collapse-item ant-collapse-item-active as class name
        // console.log('allComponentsCollapseRef', allComponentsCollapseRef.current);
        const selectedCollapseItem = _.find(allComponentsCollapseRef.current.children, item => {
          return _.includes(item.className, 'ant-collapse-item-active');
        });
        // console.log('selectedCollapseItem', selectedCollapseItem);
        if (selectedCollapseItem) {
          // make it top
          selectedCollapseItem.scrollIntoView({ behavior: 'smooth' });
        }
      }

      if (_.isInteger(selectedUngroupedFid)) {
        // console.log('allUngroupedFeaturesColumnRef', allUngroupedFeaturesColumnRef.current);
        const selectedUngroupedFeature = _.find(allUngroupedFeaturesColumnRef.current.children, item => {
          return item.getAttribute('id') === String(selectedUngroupedFid);
        });

        // console.log('selectedUngroupedFeature', selectedUngroupedFeature);

        if (selectedUngroupedFeature) {
          selectedUngroupedFeature.scrollIntoView({ behavior: 'smooth' });
        }
      }
    };

    run(selectedCid, selectedUngroupedFid);
  }, [
    selectedUngroupedFid,
    selectedCid,
  ]);

  useEffect(() => {
    if (_.isUndefined(curProduct) || _.isEmpty(curProduct)) return;

    // init mm to pixel ratio
    const initMmToPixelRatio = async (curProduct) => {
      const payload = {
        depth_map_uri: _.get(curProduct, 'inspectables[0].depth_map_uri', ''),
        u: 0,
        v: 0,
        canned_rois: [{
          shape: {
            type: 'obb',
            points: [
              { x: 0, y: 0 },
              { x: 1, y: 1 },
            ],
            angle: 0,
          },
          checklist: {},
        }]
      };

      const res = await physicalCoordMap(payload);

      if (res.error) {
        aoiAlert(t('notification.error.physicalCoordMap'), ALERT_TYPES.COMMON_ERROR);
        console.error(res.error.message);
        return;
      }

      setMmToPixelRatio(_.get(res, 'data[0].shape.points[1].x', 0) - _.get(res, 'data[0].shape.points[0].x', 0));
    };

    initMmToPixelRatio(curProduct);
  }, [curProduct]);

  useEffect(() => {
    setUngroupedFeatures(_.filter(allFeatures, f => !_.isInteger(_.get(f, 'group_id', 0))));
    if (_.isEmpty(filteredComponents) || _.isEmpty(allFeatures)) {
      setEmptyComponents([]);
      return;
    }

    setEmptyComponents(_.filter(
      filteredComponents,
      c => _.isEmpty(_.filter(allFeatures, f => f.group_id === c.region_group_id))
    ));
  }, [
    filteredComponents,
    allFeatures,
  ]);

  useEffect(() => {
    // group by mode is changed, reset selectedCid selectedFid selectedFeatureType selectedPartNo selectedPackageNo
    setSelectedCid(null);
    setSelectedFid(null);
    setSelectedFeatureType(null);
    setSelectedPartNo(null);
    setSelectedPackageNo(null);
  }, [componentListGroupMode]);

  // useEffect(() => {
  //   setSelectedFeatureType(null);
  //   setSelectedFid(null);
  //   if (_.isInteger(selectedCid) || !_.isEmpty(selectedPartNo) || !_.isEmpty(selectedPackageNo)) {
  //     setSelectedUngroupedFid(null);
  //   }
  // }, [
  //   selectedCid,
  //   selectedPartNo,
  //   selectedPackageNo,
  // ]);

  useEffect(() => {
    if (_.isInteger(selectedUngroupedFid)) {
      setSelectedCid(null);
      setSelectedFid(null);
      setSelectedFeatureType(null);
      setSelectedPartNo(null);
      setSelectedPackageNo(null);
    }
  }, [selectedUngroupedFid]);

  useEffect(() => {
    if (templateEditorShouldRefetchNotifier === 0) return;

    const run = async () => {
      dispatch(setIsContainerLvlLoadingEnabled(true));
      dispatch(setContainerLvlLoadingMsg(t('loader.refetchingFeatures')));

      await refetchAllComponents();
      await refetchAllFeatures();
      await refetchAllFeatureReevaluationResult();
      await refetchAggregatedReevaluationResult();
      setIsRedefiningInspectionRegion(false);

      dispatch(setTemplateEditorShouldRefetchNotifier(0));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
    };

    run();
  }, [templateEditorShouldRefetchNotifier]);

  useEffect(() => {
    refetchAllComponents();
    refetchAllFeatures();
    refetchAllFeatureReevaluationResult();
    refetchAggregatedReevaluationResult();

    return () => {
      const shouldUpdateModelCheck = async (productId, curProduct) => {
        const res = await shouldUpdateModels({
          model_types: [
            modelTypes.mountingModel,
            modelTypes.leadModel
          ],
          golden_product_id: Number(productId),
        });

        if (_.some(res.data, d => d)) {
          dispatch(setIsGlobalRetrainReminderOpened(true));
          dispatch(setGlobalRetrainInfo({
            productId: Number(productId),
            productName: _.get(curProduct, 'product_name', ''),
          }));
        }
      };

      shouldUpdateModelCheck(productId, curProduct);

      dispatch(setTemplateEditorShouldRefetchNotifier(0));
    };
  }, []);

  if (!_.isNumber(mmToPixelRatio) || _.isNaN(mmToPixelRatio)) return null;

  return (
    <Fragment>
      <AIDetectROI
        isOpened={isAIDetectROIOpened}
        setIsOpened={setIsAIDetectROIOpened}
      />
      <AddFromLibrary
        isOpened={isAddFromLibraryOpened}
        setIsOpened={setIsAddFromLibraryOpened}
        curProduct={curProduct}
        setCurrentNewTemplateFromLibrary={setCurrentNewTemplateFromLibrary}
      />
      <CustomModal
        width={386}
        open={isUnhealthyModalVisible}
        onCancel={() => setIsUnhealthyModalVisible(false)}
        title={<span className='font-source text-[16px] font-semibold leading-[150%] whitespace-nowrap'>
          {t('productDefine.lowConfidenceComponentsTitle')}
        </span>}
        footer={null}
      >
        <div className='flex flex-col self-stretch'>
          <div className='flex py-6 px-4 flex-col gap-4 self-stretch'>
            <div className="flex flex-col gap-2 items-center justify-center">
              <img
                src='/img/unhealthy_example.png'
                alt='unhealthy example'
                className='w-full h-auto mt-2'
                style={{ maxWidth: '100%' }}
              />
            </div>
            <span className='font-source text-[12px] font-normal leading-[150%] text-gray-4'>
              {t('productDefine.lowConfidenceComponentsMessage')}
            </span>
          </div>
          <div className='flex p-4 gap-2 items-center justify-end self-stretch'>
            <Button
              type='primary'
              onClick={() => setIsUnhealthyModalVisible(false)}
            >
              <span className='font-source text-[12px] font-semibold leading-[150%]'>
                {t('common.ok')}
              </span>
            </Button>
          </div>
        </div>
      </CustomModal>
      <div className='flex gap-0.5 flex-1 self-stretch px-0.5'>
        {isDrawModeEnabled ?
          <DrawModeCol
            setIsDrawModeEnabled={setIsDrawModeEnabled}
            selectedCid={selectedCid}
            allComponents={allComponents}
            allFeatures={allFeatures}
            refetchAllComponents={refetchAllComponents}
            refetchAllFeatures={refetchAllFeatures}
            updateAllFeaturesState={updateAllFeaturesState}
          />
        :
          <div
            className='flex w-[337px] bg-[#ffffff0d] self-stretch flex-col'
            style={{
              pointerEvents: isRedefiningInspectionRegion ? 'none' : 'auto',
              opacity: isRedefiningInspectionRegion ? 0.5 : 1,
            }}
          >
            <ConfigProvider
              theme={{
                components: {
                  Tabs: {
                    cardPadding: '6px 18px',
                  },
                }
              }}
            >
              <Tabs
                type='card'
                items={[
                  {
                    label: <span className='font-source text-sm font-normal leading-[normal]'>
                      {t('common.component')}
                    </span>,
                    key: 'component',
                  },
                  {
                    label: <span className='font-source text-sm font-normal leading-[normal]'>
                      {t('common.part')}
                    </span>,
                    key: 'part',
                  },
                  {
                    label: <span className='font-source text-sm font-normal leading-[normal]'>
                      {t('common.package')}
                    </span>,
                    key: 'package',
                  },
                ]}
                activeKey={componentListGroupMode}
                onChange={(key) => {
                  setComponentListGroupMode(key);
                  setSelectedCid(null);
                  setSelectedFid(null);
                  setSelectedUngroupedFid(null);
                  setSelectedPartNo(null);
                  setSelectedPackageNo(null);
                  setSelectedScope(null);
                }}
              />
            </ConfigProvider>
            <div className='flex p-4 gap-4 flex-col justify-center self-stretch'>
              <div className='flex flex-col self-stretch'>
                <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
                  {(() => {
                    if (componentListGroupMode === 'part') {
                      return `${t('common.part')}(${partCount})`;
                    } else if (componentListGroupMode === 'package') {
                      return `${t('common.package')}(${packageCount})`;
                    }
                    return `${t('productDefine.components')}(${componentCount})`;
                  })()}
                </span>
                <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px] text-gray-4'>
                  {t('productDefine.drawROIAndDefine')}
                </span>
              </div>
              <div className='flex flex-col self-stretch gap-2'>
                <div className='flex items-center gap-2'>
                  <Button
                    onClick={() => {
                      setSelectedCid(null);
                      setSelectedFid(null);
                      setSelectedLineItemName(null);
                      setSelectedUngroupedFid(null);
                      setIsAddFromLibraryOpened(true);
                    }}
                  >
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('productDefine.addFromLibrary')}
                    </span>
                  </Button>
                  {/* <Button
                    onClick={() => {
                      navigate(`/teach/uploadCAD?product-id=${productId}`);
                    }}
                  >
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('productDefine.importCad')}
                    </span>
                  </Button> */}
                  <div className='flex items-center gap-1'>
                    <Button
                      type= {shouldUpdateModelButtonType ? 'primary' : 'default'}
                      onClick={() => {
                        const run = async (productId) => {
                          dispatch(setIsContainerLvlLoadingEnabled(true));
                          dispatch(setContainerLvlLoadingMsg(t('loader.modelTraining')));

                          const res = await retrainTrigger({
                            model_types: [
                              modelTypes.mountingModel,
                              modelTypes.leadModel,
                            modelTypes.textVerificationModel,
                            ],
                            golden_product_id: Number(productId),
                          });

                          if (res.error) {
                            dispatch(setIsContainerLvlLoadingEnabled(false));
                            dispatch(setContainerLvlLoadingMsg(''));
                            console.error('retrainTrigger error:', _.get(res, 'error.message', ''));
                            aoiAlert(t('notification.error.retrainModel'), ALERT_TYPES.COMMON_ERROR);
                            return;
                          }

                          dispatch(setIsTrainingRunning(true));
                          dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
                          setShouldUpdateModelButtonType(false);

                          // const shouldUpdateRes = await shouldUpdateModels({
                          //   model_types: [
                          //     modelTypes.mountingModel,
                          //     modelTypes.leadModel
                          //   ]
                          // });

                          // if (_.some(shouldUpdateRes.data, d => d)) {
                          //   aoiAlert(t('notification.success.modelUpdate'), ALERT_TYPES.COMMON_SUCCESS);
                          //   setShouldUpdateModelButtonType(true);
                          // } else {
                          //   setShouldUpdateModelButtonType(false);
                          // }
                        };

                        run(productId);
                      }}
                    >
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('productDefine.retrainModel')}
                      </span>
                    </Button>
                    {/* <Tooltip
                      title={
                        <span className='font-source text-[12px] font-normal leading-[150%]'>
                          {t('productDefine.retrainModelDesc')}
                        </span>
                      }
                    >
                      <div className='flex w-7 h-7 justify-center items-center cursor-pointer rounded-[4px] hover:bg-[#ffffff0d] transition-all duration-300 ease-in-out'>
                        <img
                          src='/icn/info_white.svg'
                          alt='info'
                          className='w-3 h-3'
                        />
                      </div>
                    </Tooltip> */}
                  </div>
                  <Button
                    onClick={() => {
                      handleAutoGenerateAgentParams(productId);
                    }}
                  >
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('productDefine.autoGenerateAgentParams')}
                    </span>
                  </Button>
                </div>
                <div className='flex items-center gap-2 justify-between'>
                  <div className='flex items-center gap-2'>
                  <Select
                    mode='tags'
                    value={componentSearchValues}
                    onChange={handleComponentSearchChange}
                    placeholder={
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('productDefine.searchComponent')}
                      </span>
                    }
                    style={{ width: '200px' }}
                    options={[
                      {
                        title: 'health',
                        label: (
                          <span className='font-source text-[12px] font-normal leading-[150%]'>
                            {t('productDefine.componentHealthy')}
                          </span>
                        ),
                        options: [
                          {
                            label: (
                              <span className='font-source text-[12px] font-normal leading-[150%]'>
                                {t('productDefine.healthy')}
                              </span>
                            ),
                            value: 'healthy'
                          },
                          {
                            label: (
                              <span className='font-source text-[12px] font-normal leading-[150%]'>
                                {t('productDefine.unhealthy')}
                              </span>
                            ),
                            value: 'unhealthy'
                          }
                        ]
                      },
                      {
                        title: 'reevaluation',
                        label: (
                          <span className='font-source text-[12px] font-normal leading-[150%]'>
                            {t('productDefine.reevaluationPass')}
                          </span>
                        ),
                        options: [
                          {
                            label: (
                              <span className='font-source text-[12px] font-normal leading-[150%]'>
                                {t('productDefine.reevaluationPass')}
                              </span>
                            ),
                            value: 'reevaluation_pass'
                          },
                          {
                            label: (
                              <span className='font-source text-[12px] font-normal leading-[150%]'>
                                {t('productDefine.reevaluationFail')}
                              </span>
                            ),
                            value: 'reevaluation_fail'
                          }
                        ]
                      }
                      // {
                      //   label: (
                      //     <span className='font-source text-[12px] font-normal leading-[150%]'>
                      //       {t('productDefine.reevaluationPass')}
                      //     </span>
                      //   ),
                      //   value: 'reevaluation_pass'
                      // },
                      // {
                      //   label: (
                      //     <span className='font-source text-[12px] font-normal leading-[150%]'>
                      //       {t('productDefine.reevaluationFail')}
                      //     </span>
                      //   ),
                      //   value: 'reevaluation_fail'
                      // }
                    ]}
                  />
                  <Tooltip
                    title={
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('productDefine.componentHealthDesc')}
                      </span>
                    }
                  >
                    <div className='flex items-center justify-center w-6 h-6'>
                      <img
                        src='/icn/info_white.svg'
                        alt='info'
                        className='w-3 h-3'
                      />
                    </div>
                  </Tooltip>
                  </div>
                  <div className='flex items-center gap-2'>
                    <Tooltip
                      title={
                        <span className='font-source text-[12px] font-normal leading-[150%]'>
                          {t('productDefine.reevaluateAll')}
                        </span>
                      }
                    >
                      <div
                        className='flex w-7 h-7 justify-center items-center cursor-pointer rounded-[4px] hover:bg-[#ffffff0d] transition-all duration-300 ease-in-out'
                        onClick={async() => {
                          await handleAllFeatureReevaluateTrigger(productId);
                          setShouldTrainingSetRefetch(true);
                        }}
                      >
                        <img
                          src={'/icn/play_white_outlined.svg'}
                          alt='play'
                          className='w-3 h-3'
                        />
                      </div>
                    </Tooltip>
                  </div>
                </div>
              </div>
            </div>
            <div className='flex p-1 gap-0.5 flex-1 flex-col self-stretch'>
              <div
                style={{ height: 'calc(-344px + 100vh)' }}
                className='flex overflow-y-auto self-stretch flex-col'
              >
                {componentListGroupMode === 'component' &&
                  <GroupByComponent
                    filteredComponents={filteredComponents}
                    allFeatures={allFeatures}
                    allComponentsCollapseRef={allComponentsCollapseRef}
                    componentReevaluationResultMap={componentReevaluationResultMap}
                    selectedCid={selectedCid}
                    setSelectedCid={setSelectedCid}
                    setSelectedFid={setSelectedFid}
                    setSelectedAgentParam={setSelectedAgentParam}
                    setSelectedUngroupedFid={setSelectedUngroupedFid}
                    setRequiredLocateRect={setRequiredLocateRect}
                    setSelectedFeatureType={setSelectedFeatureType}
                    selectedFeatureType={selectedFeatureType}
                    selectedUngroupedFid={selectedUngroupedFid}
                    setSelectedPackageNo={setSelectedPackageNo}
                    setSelectedPartNo={setSelectedPartNo}
                    setSelectedScope={setSelectedScope}
                    refetchAllComponents={refetchAllComponents}
                    allFeatureReevaluationResult={allFeatureReevaluationResult}
                    setSelectedArrayIndex={setSelectedArrayIndex}
                    aggregatedReevaluationResult={aggregatedReevaluationResult}
                  />
                }
                {componentListGroupMode === 'part' &&
                  <GroupByPartNo
                    filteredComponents={filteredComponents}
                    allFeatures={allFeatures}
                    selectedPartNo={selectedPartNo}
                    setSelectedPartNo={setSelectedPartNo}
                    allPartsCollapseRef={allPartsCollapseRef}
                    setSelectedFeatureType={setSelectedFeatureType}
                    selectedFeatureType={selectedFeatureType}
                    selectedCid={selectedCid}
                    setSelectedCid={setSelectedCid}
                    setSelectedFid={setSelectedFid}
                    setRequiredLocateRect={setRequiredLocateRect}
                    setSelectedPackageNo={setSelectedPackageNo}
                    setSelectedScope={setSelectedScope}
                    setSelectedAgentParam={setSelectedAgentParam}
                    setSelectedUngroupedFid={setSelectedUngroupedFid}
                    selectedUngroupedFid={selectedUngroupedFid}
                    // componentReevaluationResultMap={componentReevaluationResultMap}
                    allFeatureReevaluationResult={allFeatureReevaluationResult}
                    selectedArrayIndex={selectedArrayIndex}
                    setSelectedArrayIndex={setSelectedArrayIndex}
                    aggregatedReevaluationResult={aggregatedReevaluationResult}
                  />
                }
                {componentListGroupMode === 'package' &&
                  <GroupByPackageNo
                    filteredComponents={filteredComponents}
                    allFeatures={allFeatures}
                    selectedPackageNo={selectedPackageNo}
                    setSelectedPackageNo={setSelectedPackageNo}
                    allPackagesCollapseRef={allPackagesCollapseRef}
                    selectedFeatureType={selectedFeatureType}
                    setSelectedFeatureType={setSelectedFeatureType}
                    setSelectedPartNo={setSelectedPartNo}
                    setSelectedCid={setSelectedCid}
                    setSelectedFid={setSelectedFid}
                    setRequiredLocateRect={setRequiredLocateRect}
                    setSelectedScope={setSelectedScope}
                    setSelectedAgentParam={setSelectedAgentParam}
                    setSelectedUngroupedFid={setSelectedUngroupedFid}
                    selectedUngroupedFid={selectedUngroupedFid}
                    allFeatureReevaluationResult={allFeatureReevaluationResult}
                    selectedCid={selectedCid}
                    selectedPartNo={selectedPartNo}
                    selectedArrayIndex={selectedArrayIndex}
                    setSelectedArrayIndex={setSelectedArrayIndex}
                    aggregatedReevaluationResult={aggregatedReevaluationResult}
                  />
                }
                {!_.isEmpty(ungroupedFeatures) &&
                  <UngroupedFeature
                    allFeatures={allFeatures}
                    selectedUngroupedFid={selectedUngroupedFid}
                    setSelectedUngroupedFid={setSelectedUngroupedFid}
                    setRequiredLocateRect={setRequiredLocateRect}
                    setSelectedPartNo={setSelectedPartNo}
                    setSelectedPackageNo={setSelectedPackageNo}
                    setSelectedScope={setSelectedScope}
                    setSelectedAgentParam={setSelectedAgentParam}
                    setSelectedCid={setSelectedCid}
                    setSelectedFid={setSelectedFid}
                  />
                }
                <div className="mt-auto">
                  <ComponentStatus
                  allComponents={allComponents}
                  // componentReevaluationResultMap={componentReevaluationResultMap}
                  allFeatureReevaluationResult={allFeatureReevaluationResult}
                  componentListGroupMode={componentListGroupMode}
                />
                </div>
              </div>
            </div>
          </div>
        }
        <div className='flex flex-1 self-stretch'>
          {!isDrawModeEnabled && !isRedefiningInspectionRegion &&
            <Display
              curProduct={curProduct}
              allFeatures={allFeatures}
              selectedFid={selectedFid}
              selectedCid={selectedCid}
              selectedLineItemName={selectedLineItemName}
              setSelectedFid={setSelectedFid}
              currentNewTemplateFromLibrary={currentNewTemplateFromLibrary}
              setCurrentNewTemplateFromLibrary={setCurrentNewTemplateFromLibrary}
              fcanvasRef={fcanvasRef}
              allComponents={allComponents}
              setSelectedCid={setSelectedCid}
              setSelectedLineItemName={setSelectedLineItemName}
              refetchAllFeatures={refetchAllFeatures}
              updateAllFeaturesState={updateAllFeaturesState}
              refetchAllComponents={refetchAllComponents}
              updateAllComponentsState={updateAllComponentsState}
              requiredLocateRect={requiredLocateRect}
              selectedUngroupedFid={selectedUngroupedFid}
              setSelectedUngroupedFid={setSelectedUngroupedFid}
              setHoveredCid={setHoveredCid}
              setHoveredFid={setHoveredFid}
              setHoveredLineItemName={setHoveredLineItemName}
              selectedAgentParam={selectedAgentParam}
              setSelectedAgentParam={setSelectedAgentParam}
              refetchCurProduct={refetchCurProduct}
              updatedFeatures={updatedFeatures}
              setUpdatedFeatures={setUpdatedFeatures}
              allFeaturesRef={allFeaturesRef}
              allComponentsRef={allComponentsRef}
              mmToPixelRatio={mmToPixelRatio}
              refetchAllFeatureReevaluationResult={refetchAllFeatureReevaluationResult}
              refetchAggregatedReevaluationResult={refetchAggregatedReevaluationResult}
              selectedArrayIndex={selectedArrayIndex}
              setSelectedArrayIndex={setSelectedArrayIndex}
              setSelectedFeatureType={setSelectedFeatureType}
              setSelectedPartNo={setSelectedPartNo}
              setSelectedPackageNo={setSelectedPackageNo}
              setSelectedScope={setSelectedScope}
              componentDetailWidth={componentDetailWidth}
            />
          }
          {isDrawModeEnabled && !isRedefiningInspectionRegion &&
            <TemplateEditorDrawModeViewer
              curProduct={curProduct}
              allComponents={allComponents}
              selectedCid={selectedCid}
              refetchAllComponents={refetchAllComponents}
              refetchAllFeatures={refetchAllFeatures}
              updateAllFeaturesState={updateAllFeaturesState}
              allFeatures={allFeatures}
            />
          }
          {!isDrawModeEnabled && isRedefiningInspectionRegion &&
            <TemplateEditorRefineInspectionRegion
              curProduct={curProduct}
              refetchAllFeatures={refetchAllFeatures}
              refetchAllComponents={refetchAllComponents}
              refetchAllFeatureReevaluationResult={refetchAllFeatureReevaluationResult}
              refetchAggregatedReevaluationResult={refetchAggregatedReevaluationResult}
              setIsRedefiningInspectionRegion={setIsRedefiningInspectionRegion}
            />
          }
        </div>
        { !isDrawModeEnabled &&
          <div className='flex'>
            <div
              className={`flex w-[4px] self-stretch cursor-col-resize bg-gray-600 hover:bg-blue-500 transition-colors duration-200 ${
                isResizingComponentDetail ? 'bg-blue-500' : ''
              }`}
              onMouseDown={handleComponentDetailMouseDown}
              title="拖拽调整侧边栏宽度"
            >
              <div className='flex w-full h-full items-center justify-center'>
                <div className='h-8 w-0.5 bg-white rounded opacity-60'></div>
              </div>
            </div>
            <div style={{ width: `${componentDetailWidth}px` }}>
              <ComponentDetail
                allComponents={allComponents}
                allFeatures={allFeatures}
                selectedFid={selectedFid}
                selectedCid={selectedCid}
                componentListGroupMode={componentListGroupMode}
                selectedFeatureType={selectedFeatureType}
                selectedPartNo={selectedPartNo}
                selectedPackageNo={selectedPackageNo}
                setSelectedFid={setSelectedFid}
                selectedLineItemName={selectedLineItemName}
                setIsAddFromLibraryOpened={setIsAddFromLibraryOpened}
                setIsDrawModeEnabled={setIsDrawModeEnabled}
                refetchAllFeatures={refetchAllFeatures}
                updateAllFeaturesState={updateAllFeaturesState}
                setSelectedAgentParam={setSelectedAgentParam}
                goldenProductId={Number(productId)}
                refetchAllComponents={refetchAllComponents}
                selectedUngroupedFid={selectedUngroupedFid}
                trainingSetSelectedDetail={trainingSetSelectedDetail}
                setTrainingSetSelectedDetail={setTrainingSetSelectedDetail}
                trainingSetSelectedErrorType={trainingSetSelectedErrorType}
                setTrainingSetSelectedErrorType={setTrainingSetSelectedErrorType}
                refetchAllFeatureReevaluationResult={refetchAllFeatureReevaluationResult}
                refetchAggregatedReevaluationResult={refetchAggregatedReevaluationResult}
                setShouldUpdateModelButtonType={setShouldUpdateModelButtonType}
                selectedScope={selectedScope}
                isRedefiningInspectionRegion={isRedefiningInspectionRegion}
                setSelectedCid={setSelectedCid}
                setRequiredLocateRect={setRequiredLocateRect}
                selectedArrayIndex={selectedArrayIndex}
                setSelectedArrayIndex={setSelectedArrayIndex}
                mmToPixelRatio={mmToPixelRatio}
                isPesudoColorDisplayed={isPesudoColorDisplayed}
                setIsPesudoColorDisplayed={setIsPesudoColorDisplayed}
                shouldTrainingSetRefetch={shouldTrainingSetRefetch}
                setShouldTrainingSetRefetch={setShouldTrainingSetRefetch}
              />
            </div>
          </div>
        }
      </div>
    </Fragment>
  );
};

export default TemplateEditor;