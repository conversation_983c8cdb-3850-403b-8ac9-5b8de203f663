import { fabric } from 'fabric';
import { backgroundRoi1, backgroundRoi2, barcodeScanner, componentRoiPadding, defaultDashedArray, defaultNewFeatureExtRoiPadding, directionArrow, extBottom, extendedRoi, extendedRoiRelatedLineItems, extTop, extBot, highResoluBgMegaPixelCount, highResoluCroppedDisplayMegaPixelCount, lead3DExtTop, leadFeatureType, leadInspection2D, leadInspection3D, leadSegmentationRects, localStorageKeys, maskRoi, mounting3DExtendedRoiAgentParamNames, mountingFeatureType, mountingInspection2D, mountingInspection3D, newRectStrokeWidth, pointCloudColorMode, polarityRoi, profileHeight, profileRoi, profileWidth, sceneCopyRectBottomRightOffset, serverHost, solderFeatureType, solderInspection3D, textFeatureType, textVerification, isAOI2DSMT, leadInspection2DBase, solderInspection2D, isAOI3DSMT } from '../common/const';
import _ from 'lodash';
import { getColorByStr, sleep } from '../common/util';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { text } from '../common/translation';
import i18n from '../i18n';


export const removeProfileHeightFromPayload = (payload) => {
  const newPayload = _.cloneDeep(payload);
  if (_.has(newPayload, `line_item_params.${solderInspection3D}.params.${profileHeight}`)) {
    delete newPayload.line_item_params[solderInspection3D].params[profileHeight];
  }
  return newPayload;
};


export const loadInitFullSizeThumbnail = async ({
  fcanvas,
  rawWidth,
  rawHeight,
  thumbnailBgSceneRef,
  imageUri,
  depthUri,
  type,
}) => {
  if (!fcanvas) return;

  if (thumbnailBgSceneRef.current) {
    fcanvas.remove(thumbnailBgSceneRef.current);
    thumbnailBgSceneRef.current = null;
  }

  // fetch and load
  // 1 mega pixel = 1 million pixels
  // we use 10 million pixels for bg scene
  let url = `${serverHost}/blob?type=${type}&color_uri=${encodeURIComponent(imageUri)}&depth_uri=${encodeURIComponent(depthUri)}&max_megapixel=${highResoluBgMegaPixelCount}`;
  url += `&t=${Date.now().valueOf()}`; // add timestamp to avoid cache
  const img = await new Promise((resolve, reject) => {
    fabric.util.loadImage(url, (img) => {
      resolve(img);
    });
  });

  const thumbnailBgScene = new fabric.Image(img, {
    selectable: false,
    evented: false,
  });

  // thumbnail has a different size then the raw image hence scale to fit
  thumbnailBgScene.scaleToWidth(rawWidth);
  thumbnailBgScene.scaleToHeight(rawHeight);

  thumbnailBgSceneRef.current = thumbnailBgScene;

  fcanvas.add(thumbnailBgScene);
};

export const loadHighResolScene = async ({
  fcanvasRef,
  rawImageW,
  rawImageH,
  displayedHighResolSceneRef,
  imageUri,
  depthUri,
  type,
  callback,
}) => {
  if (!fcanvasRef.current) return;

  // calc the current view port's bounding box in raw image's coordinate based on current zoom and the view port's position at the thumbnailBgScene
  const zoom = fcanvasRef.current.getZoom();
  const vpLeft = fcanvasRef.current.viewportTransform[4];
  const vpTop = fcanvasRef.current.viewportTransform[5];
  const vpWidth = fcanvasRef.current.getWidth();
  const vpHeight = fcanvasRef.current.getHeight();

  let rawVpLeft = -(vpLeft / zoom);
  let rawVpTop = -(vpTop / zoom);
  let rawVpRight = -((vpLeft - vpWidth) / zoom);
  let rawVpBottom = -((vpTop - vpHeight) / zoom);

  // make sure within 0-rawImageW and 0-rawImageH
  rawVpLeft = Math.min(Math.max(0, rawVpLeft), rawImageW);
  rawVpTop = Math.min(Math.max(0, rawVpTop), rawImageH);
  rawVpRight = Math.min(Math.max(0, rawVpRight), rawImageW);
  rawVpBottom = Math.min(Math.max(0, rawVpBottom), rawImageH);
  
  // round to integer
  rawVpLeft = Math.ceil(rawVpLeft);
  rawVpTop = Math.ceil(rawVpTop);
  rawVpRight = Math.floor(rawVpRight);
  rawVpBottom = Math.floor(rawVpBottom);
  
  // console.log('rawVpLeft', rawVpLeft, 'rawVpTop', rawVpTop, 'rawVpRight', rawVpRight, 'rawVpBottom', rawVpBottom);

  // // if viewport has no intersection with the raw image, return
  if (rawVpLeft >= rawVpRight || rawVpTop >= rawVpBottom) return;

  // if viewport overlaps with the raw image, return
  // now this is checked before reload is called
  // if (rawVpLeft === 0 && rawVpTop === 0 && rawVpRight === rawImageW && rawVpBottom === rawImageH) return;
  
  // fetch and load
  let url = `${serverHost}/blob?type=${type}&color_uri=${encodeURIComponent(imageUri)}&depth_uri=${encodeURIComponent(depthUri)}&x_min=${rawVpLeft}&y_min=${rawVpTop}&x_max=${rawVpRight-1}&y_max=${rawVpBottom-1}&max_megapixel=${highResoluCroppedDisplayMegaPixelCount}`;
  url += `&t=${Date.now().valueOf()}`; // add timestamp to avoid cache
  const img = await new Promise((resolve, reject) => {
    fabric.util.loadImage(url, (img) => {
      resolve(img);
    });
  });

  const tmpHighResolScene = new fabric.Image(img, {
    selectable: false,
    evented: false,
    // top: rawVpTop,
    // left: rawVpLeft,
    // imageSmoothing: false,
  });

  const scaleX = (rawVpRight - rawVpLeft)/img.width;
  const scaleY = (rawVpBottom - rawVpTop)/img.height;

  if (scaleX !== 1) tmpHighResolScene.scale(_.min([scaleX, scaleY]));
  tmpHighResolScene.set({
    top: rawVpTop,
    left: rawVpLeft,
  });
  tmpHighResolScene.setCoords();

  fcanvasRef.current.add(tmpHighResolScene);

  // dispose the previous high resol scene
  if (displayedHighResolSceneRef.current) {
    fcanvasRef.current.remove(displayedHighResolSceneRef.current);
    displayedHighResolSceneRef.current = null;
  }

  displayedHighResolSceneRef.current = tmpHighResolScene;

  if (callback) callback();
};

export const generatePCBasedOnModes = ({
  pointNColor,
  mode: mode,
  zRoiMode: zRoiMode,
  zRoiMin: zRoiMin,
  zRoiMax: zRoiMax,
}) => {
  const {
    positions, // x, y, z, x, y, z, ...
    colors, // r, g, b, r, g, b, ...
  } = pointNColor;

  let tmpLength = 0;
	let centerX = 0;
	let centerY = 0;
	let centerZ = 0;

  let zMin = Number.POSITIVE_INFINITY;
  let zMax = Number.NEGATIVE_INFINITY;

  if (mode === pointCloudColorMode.FULL_COLOR && zRoiMode === 'off') {
    for (let i = 0; i < positions.length; i += 3) {
      if (isNaN(positions[i]) || isNaN(positions[i + 1]) || isNaN(positions[i + 2])) continue;
      if (positions[i + 2] < zMin) {
        zMin = positions[i + 2];
      }
      if (positions[i + 2] > zMax) {
        zMax = positions[i + 2];
      }
      centerX += positions[i];
      centerY += positions[i + 1];
      centerZ += positions[i + 2];
      tmpLength += 1;
    }
    return {
      positions,
      colors,
      colorsModified: false,
      center: {
        x: centerX / tmpLength,
        y: centerY / tmpLength,
        z: centerZ / tmpLength,
      },
      zMin,
      zMax,
    };
  }

  const newPositions = new Float32Array(positions.length);
  const newColors = new Float32Array(colors.length);

  const numPoints = positions.length / 3;
  let colorsModified = false;

  if (zRoiMode === 'off') {
    if (mode === pointCloudColorMode.GREEN_MODE) {
      for (let i = 0; i < numPoints; i += 1) {
        if (isNaN(positions[3 * i]) || isNaN(positions[3 * i + 1]) || isNaN(positions[3 * i + 2])) continue;
        newPositions[3 * i] = positions[3 * i];
        newPositions[3 * i + 1] = positions[3 * i + 1];
        newPositions[3 * i + 2] = positions[3 * i + 2];
        newColors[3 * i] = 0;
        newColors[3 * i + 1] = 1;
        newColors[3 * i + 2] = 0;
        centerX += positions[3 * i];
        centerY += positions[3 * i + 1];
        centerZ += positions[3 * i + 2];
        tmpLength += 1;
        if (positions[3 * i + 2] < zMin) {
          zMin = positions[3 * i + 2];
        }
        if (positions[3 * i + 2] > zMax) {
          zMax = positions[3 * i + 2];
        }
      }
      colorsModified = true;
    } else if (mode === pointCloudColorMode.GRAY_SCALE) {
      for (let i = 0; i < numPoints; i += 1) {
        if (isNaN(positions[3 * i]) || isNaN(positions[3 * i + 1]) || isNaN(positions[3 * i + 2])) continue;
        if (positions[3 * i + 2] < zMin) {
          zMin = positions[3 * i + 2];
        }
        if (positions[3 * i + 2] > zMax) {
          zMax = positions[3 * i + 2];
        }
      }
      const range = zMax - zMin;
      for (let i = 0; i < numPoints; i += 1) {
        if (isNaN(positions[3 * i]) || isNaN(positions[3 * i + 1]) || isNaN(positions[3 * i + 2])) continue;

        // newPositions.push(positions[3 * i], positions[3 * i + 1], positions[3 * i + 2]);
        // newColors.push((positions[3 * i + 2] - zMin) / range, (positions[3 * i + 2] - zMin) / range, (positions[3 * i + 2] - zMin) / range);
        newPositions[3 * i] = positions[3 * i];
        newPositions[3 * i + 1] = positions[3 * i + 1];
        newPositions[3 * i + 2] = positions[3 * i + 2];
        newColors[3 * i] = (positions[3 * i + 2] - zMin) / range;
        newColors[3 * i + 1] = (positions[3 * i + 2] - zMin) / range;
        newColors[3 * i + 2] = (positions[3 * i + 2] - zMin) / range;

        centerX += positions[3 * i];
        centerY += positions[3 * i + 1];
        centerZ += positions[3 * i + 2];
        tmpLength += 1;
      }
      colorsModified = true;
    } else if (mode === pointCloudColorMode.DEPTH_MAP) {
      function hueShift(h, s) {
        let f = (n, k = (n + h * 12) % 12) =>
          s * Math.max(0, Math.min(Math.min(k - 3, 9 - k, 1), 1));
        return [f(0), f(8), f(4)];
      }

      for (let i = 0; i < numPoints; i += 1) {
        if (positions[3 * i + 2] < zMin) {
          zMin = positions[3 * i + 2];
        }
        if (positions[3 * i + 2] > zMax) {
          zMax = positions[3 * i + 2];
        }
      }
      const range = zMax - zMin;

      for (let i = 0; i < numPoints; i += 1) {
        if (isNaN(positions[3 * i]) || isNaN(positions[3 * i + 1]) || isNaN(positions[3 * i + 2])) continue;

        const t = (positions[3 * i + 2] - zMin) / range;
        const hue = t * 0.5;
        const saturation = 0.5;
        const interpolatedColor = hueShift(hue, saturation);

        // newPositions.push(positions[3 * i], positions[3 * i + 1], positions[3 * i + 2]);
        // newColors.push(interpolatedColor[0], interpolatedColor[1], interpolatedColor[2]);
        newPositions[3 * i] = positions[3 * i];
        newPositions[3 * i + 1] = positions[3 * i + 1];
        newPositions[3 * i + 2] = positions[3 * i + 2];
        newColors[3 * i] = interpolatedColor[0];
        newColors[3 * i + 1] = interpolatedColor[1];
        newColors[3 * i + 2] = interpolatedColor[2];
        centerX += positions[3 * i];
        centerY += positions[3 * i + 1];
        centerZ += positions[3 * i + 2];
        tmpLength += 1;
      }
      colorsModified = true;
    }
  } else {
    if (mode === pointCloudColorMode.FULL_COLOR) {
      for (let i = 0; i < numPoints; i += 1) {
        if (isNaN(positions[3 * i]) || isNaN(positions[3 * i + 1]) || isNaN(positions[3 * i + 2])) continue;

        if (zRoiMin < positions[3 * i + 2] && positions[3 * i + 2] < zRoiMax) {
          // newPositions.push(positions[3 * i], positions[3 * i + 1], positions[3 * i + 2]);
          // newColors.push(colors[3 * i], colors[3 * i + 1], colors[3 * i + 2]);
          newPositions[3 * i] = positions[3 * i];
          newPositions[3 * i + 1] = positions[3 * i + 1];
          newPositions[3 * i + 2] = positions[3 * i + 2];
          newColors[3 * i] = colors[3 * i];
          newColors[3 * i + 1] = colors[3 * i + 1];
          newColors[3 * i + 2] = colors[3 * i + 2];

          centerX += positions[3 * i];
          centerY += positions[3 * i + 1];
          centerZ += positions[3 * i + 2];
          tmpLength += 1;
          if (positions[3 * i + 2] < zMin) {
            zMin = positions[3 * i + 2];
          }
          if (positions[3 * i + 2] > zMax) {
            zMax = positions[3 * i + 2];
          }
        }
      }
    } else if (mode === pointCloudColorMode.GREEN_MODE) {
      for (let i = 0; i < numPoints; i += 1) {
        if (isNaN(positions[3 * i]) || isNaN(positions[3 * i + 1]) || isNaN(positions[3 * i + 2])) continue;

        if (zRoiMin < positions[3 * i + 2] && positions[3 * i + 2] < zRoiMax) {
          // newPositions.push(positions[3 * i], positions[3 * i + 1], positions[3 * i + 2]);
          // newColors.push(0, 1, 0);
          newPositions[3 * i] = positions[3 * i];
          newPositions[3 * i + 1] = positions[3 * i + 1];
          newPositions[3 * i + 2] = positions[3 * i + 2];
          newColors[3 * i] = 0;
          newColors[3 * i + 1] = 1;
          newColors[3 * i + 2] = 0;

          centerX += positions[3 * i];
          centerY += positions[3 * i + 1];
          centerZ += positions[3 * i + 2];
          tmpLength += 1;
          if (positions[3 * i + 2] < zMin) {
            zMin = positions[3 * i + 2];
          }
          if (positions[3 * i + 2] > zMax) {
            zMax = positions[3 * i + 2];
          }
        }
      }
      colorsModified = true;
    } else if (mode === pointCloudColorMode.GRAY_SCALE) {

      for (let i = 0; i < numPoints; i += 1) {
        if (isNaN(positions[3 * i]) || isNaN(positions[3 * i + 1]) || isNaN(positions[3 * i + 2])) continue;

        if (zRoiMin < positions[3 * i + 2] && positions[3 * i + 2] < zRoiMax) {
          if (positions[3 * i + 2] < zMin) {
            zMin = positions[3 * i + 2];
          }
          if (positions[3 * i + 2] > zMax) {
            zMax = positions[3 * i + 2];
          }
        }
      }

      const range = zMax - zMin;
      for (let i = 0; i < numPoints; i += 1) {
        if (isNaN(positions[3 * i]) || isNaN(positions[3 * i + 1]) || isNaN(positions[3 * i + 2])) continue;

        if (zRoiMin < positions[3 * i + 2] && positions[3 * i + 2] < zRoiMax) {
          // newPositions.push(positions[3 * i], positions[3 * i + 1], positions[3 * i + 2]);
          // newColors.push((positions[3 * i + 2] - zMin) / range, (positions[3 * i + 2] - zMin) / range, (positions[3 * i + 2] - zMin) / range);
          newPositions[3 * i] = positions[3 * i];
          newPositions[3 * i + 1] = positions[3 * i + 1];
          newPositions[3 * i + 2] = positions[3 * i + 2];
          newColors[3 * i] = (positions[3 * i + 2] - zMin) / range;
          newColors[3 * i + 1] = (positions[3 * i + 2] - zMin) / range;
          newColors[3 * i + 2] = (positions[3 * i + 2] - zMin) / range;

          centerX += positions[3 * i];
          centerY += positions[3 * i + 1];
          centerZ += positions[3 * i + 2];
          tmpLength += 1;
        }
      }
      colorsModified = true;
    } else if (mode === pointCloudColorMode.DEPTH_MAP) {
      function hueShift(h, s) {
        let f = (n, k = (n + h * 12) % 12) =>
          s * Math.max(0, Math.min(Math.min(k - 3, 9 - k, 1), 1));
        return [f(0), f(8), f(4)];
      }
      for (let i = 0; i < numPoints; i += 1) {
        if (isNaN(positions[3 * i]) || isNaN(positions[3 * i + 1]) || isNaN(positions[3 * i + 2])) continue;
        if (zRoiMin < positions[3 * i + 2] && positions[3 * i + 2] < zRoiMax) {
          if (positions[3 * i + 2] < zMin) {
            zMin = positions[3 * i + 2];
          }
          if (positions[3 * i + 2] > zMax) {
            zMax = positions[3 * i + 2];
          }
        }
      }
      const range = zMax - zMin;

      for (let i = 0; i < numPoints; i += 1) {
        if (isNaN(positions[3 * i]) || isNaN(positions[3 * i + 1]) || isNaN(positions[3 * i + 2])) continue;

        if (zRoiMin < positions[3 * i + 2] && positions[3 * i + 2] < zRoiMax) {
          const t = (positions[3 * i + 2] - zMin) / range;
          const hue = t * 0.5;
          const saturation = 0.5;
          const interpolatedColor = hueShift(hue, saturation);
          // newPositions.push(positions[3 * i], positions[3 * i + 1], positions[3 * i + 2]);
          // newColors.push(interpolatedColor[0], interpolatedColor[1], interpolatedColor[2]);
          newPositions[3 * i] = positions[3 * i];
          newPositions[3 * i + 1] = positions[3 * i + 1];
          newPositions[3 * i + 2] = positions[3 * i + 2];
          newColors[3 * i] = interpolatedColor[0];
          newColors[3 * i + 1] = interpolatedColor[1];
          newColors[3 * i + 2] = interpolatedColor[2];

          centerX += positions[3 * i];
          centerY += positions[3 * i + 1];
          centerZ += positions[3 * i + 2];
          tmpLength += 1;
        }
      }
      colorsModified = true;
    }
  }

  return {
    positions: newPositions,
    colors: newColors,
    colorsModified,
    center: {
      x: centerX / tmpLength,
      y: centerY / tmpLength,
      z: centerZ / tmpLength,
    },
    zMin,
    zMax,
  };
};

export const loadAndDecodePoints = async (url) => {
  // console.log(Date.now().valueOf(), 'start downloading pointcloud');
  const response = await fetch(url, {
    headers: {
      'Authorization': localStorage.getItem(localStorageKeys.accessToken) || '',
    }
  });
  // console.log(Date.now().valueOf(), 'finish downloading pointcloud');
  
  // console.log(Date.now().valueOf(), 'start decoding pointcloud');
  const blob = await response.arrayBuffer();
  const buffer = new Uint8Array(blob);

  const pointSize = 15; // 3 floats (4 bytes each) + 3 bytes for RGB
  const numPoints = buffer.length / pointSize;

  const positions = new Float32Array(buffer.buffer, 0, numPoints * 3);
  const colors = new Uint8Array(buffer.buffer, 0 + numPoints * 12, numPoints * 3);
  // console.log(Date.now().valueOf(), 'finish decoding pointcloud');

  for (let i = 0; i < positions.length; i += 3) {
    if (positions[i] === 0 && positions[i + 1] === 0 && positions[i + 2] === 0) {
      positions[i] = NaN;
      positions[i + 1] = NaN;
      positions[i + 2] = NaN;
      colors[i] = NaN;
      colors[i + 1] = NaN;
      colors[i + 2] = NaN;
    }
    if (!isFinite(positions[i]) || !isFinite(positions[i + 1]) || !isFinite(positions[i + 2])) {
      positions[i] = NaN;
      positions[i + 1] = NaN;
      positions[i + 2] = NaN;
      colors[i] = NaN;
      colors[i + 1] = NaN;
      colors[i + 2] = NaN;
    }
  }

  return {positions, colors};
};

/**
 * Get object3D from scene by object id
 * @param {THREE.Scene} scene 
 * @param {Number} objectId 
 * @returns {THREE.Object3D | null}
 */
export const getObjectFromScene = (scene, objectId) => {
	for (const obj of scene.children) {
		if (obj.id === objectId) {
			return obj;
		}
	}
	return null;
};

/**
 * Dispose point cloud from scene in threejs
 * @param {THREE.Scene} scene 
 * @param {Number} cloudId 
 */
export const disposePointCloud = (scene, cloudId) => {
  const cloud = getObjectFromScene(scene, cloudId);
  if (_.isEmpty(cloud)) return;
  
  scene.remove(cloud);
	// dispose geometry
	if (cloud.geometry) cloud.geometry.dispose();
	// dispose material
	if (cloud.material) {
		if (Array.isArray(cloud.material)) {
			cloud.material.forEach((material) => {
				if (material.map) material.map.dispose();
				material.dispose();
			});
		} else {
			if (cloud.material.map) cloud.material.map.dispose();
			cloud.material.dispose();
		}
	}
};

/**
 * Detach threejs TransformControls from object
 * @param {TransformControls} control
 */
export const detachTransformControlsFromObject = (control) => {
	control.detach();
	control.removeEventListener('dragging-changed');
};

export const generalPanZoomMouseDownHandler = (opt, fcanvasRef, isPanningRef) => {
  if (opt.target) {
    isPanningRef.current = false;
    return;
  }
  
  isPanningRef.current = true;
  fcanvasRef.current.selection = false;
  fcanvasRef.current.setCursor('grab');
};

export const middlePanZoomMouseDownHandler = (fcanvasRef, isPanningRef) => {
  isPanningRef.current = true;
  fcanvasRef.current.selection = false;
  fcanvasRef.current.setCursor('grab');
};

export const generalPanZoomMouseMoveHandler = (opt, fcanvasRef, isPanningRef) => {
  if (isPanningRef.current && opt && opt.e) {
    fcanvasRef.current.setCursor('grab');
    const delta = new fabric.Point(opt.e.movementX, opt.e.movementY);
    fcanvasRef.current.relativePan(delta);
  }
};

export const generalPanZoomMouseUpHandler = (fcanvasRef, isPanningRef) => {
  isPanningRef.current = false;
  fcanvasRef.current.setCursor('default');
};

export const generalPanZoomMouseWheelHandler = (opt, fcanvasRef) => {
  const delta = opt.e.deltaY;
  let zoom = fcanvasRef.current.getZoom();
  zoom *= 0.999 ** delta;
  fcanvasRef.current.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
  opt.e.preventDefault();
  opt.e.stopPropagation();
};

export const getTwoDRectPminPmax = (rect, strokeWidth) => {
	const pMin = { x: _.round(rect.left + strokeWidth, 0), y: _.round(rect.top + strokeWidth, 0) };
	const pMax = { x: _.round(rect.left + rect.width * rect.scaleX - 1, 0), y: _.round(rect.top + rect.height * rect.scaleY - 1, 0) };

  return { pMin, pMax };
};

export const getRotatedPointPos = (center, pos, deg) => {
  // given a point pos, rotate it around center by deg degree
  const rad = deg * Math.PI / 180;
  const cos = Math.cos(rad);
  const sin = Math.sin(rad);
  const x = center.x + (pos.x - center.x) * cos - (pos.y - center.y) * sin;
  const y = center.y + (pos.x - center.x) * sin + (pos.y - center.y) * cos;
  return { x, y };
};

export const flip2DPointBasedOnPoint = (target, reference, axis) => {
  // flip target point based on reference point vertically or horizontally
  switch (axis) {
    case 'vertical':
      return { x: target.x, y: 2 * reference.y - target.y };
    case 'horizontal':
      return { x: 2 * reference.x - target.x, y: target.y };
    default:
      return target;
  }
};

export const getFabricViewportCenter = (fcanvas) => {
  const zoom = fcanvas.getZoom();
  const vpt = fcanvas.viewportTransform; // [scaleX, skewX, skewY, scaleY, translateX, translateY]
  const centerX = (fcanvas.width / 2 - vpt[4]) / zoom;
  const centerY = (fcanvas.height / 2 - vpt[5]) / zoom;
  
  return new fabric.Point(centerX, centerY);
};

export const generateLineItemSpecialDisplayObject = (feature) => {
  // 
};

export const getZoomNeededForObjs = (objs, fcanvas, zoomOutRatio=0.4) => {
  if (!fcanvas || !objs || objs.length === 0) return;

  const aabbBBox = fabric.util.groupSVGElements(objs, {}).getBoundingRect(true);

  const {
    width,
    height,
  } = aabbBBox;

  const canvasWidth = fcanvas.getWidth();
  const canvasHeight = fcanvas.getHeight();

  let zoom = Math.min(
    canvasWidth/ width,
    canvasHeight / height,
  );
  zoom -= zoom * zoomOutRatio;

  return zoom;
};

export const zoomPanToObjects = (objs, fcanvas, zoomOutRatio=0.4) => {
  if (!fcanvas || !objs || objs.length === 0) return;

  const aabbBBox = fabric.util.groupSVGElements(objs, {}).getBoundingRect(true);
  const aabbBBoxRect = new fabric.Rect({
    left: aabbBBox.left,
    top: aabbBBox.top,
    width: aabbBBox.width,
    height: aabbBBox.height,
  });

  zoomPanToObject(aabbBBoxRect, fcanvas, zoomOutRatio);
};

export const zoomPanToObject = (obj, fcanvas, zoomOutRatio=0.4, objStrokeWidth) => {
  if (!fcanvas || !obj) return;

  // const strokeWidth = _.isUndefined(objStrokeWidth) ? obj.strokeWidth : objStrokeWidth;

  const aabbBBox = obj.getBoundingRect(true);

  const {
    width,
    height,
  } = aabbBBox;

  const rectCenter = new fabric.Point(
    aabbBBox.left + width / 2,
    aabbBBox.top + height / 2
  );

  const canvasWidth = fcanvas.getWidth();
  const canvasHeight = fcanvas.getHeight();

  let zoom = Math.min(
    canvasWidth/ width,
    canvasHeight / height,
  );
  zoom -= zoom * zoomOutRatio;

  fcanvas.zoomToPoint(rectCenter, zoom);

  const newRectCenter = fabric.util.transformPoint(rectCenter, fcanvas.viewportTransform);

  // Calculate the pan adjustment to center the cropped area
  const panX = (canvasWidth / 2 - newRectCenter.x) + fcanvas.viewportTransform[4];
  const panY = (canvasHeight / 2 - newRectCenter.y) + fcanvas.viewportTransform[5];

  // Apply the pan adjustment
  fcanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];

  // Re-render the canvas
  fcanvas.requestRenderAll();
};

export const getComponentRoiPminPaxByFeature = (features, fcanvas) => {
  fcanvas.discardActiveObject();
  const selection = new fabric.ActiveSelection(features, {
    canvas: fcanvas,
  });

  const center = selection.getCenterPoint();

  fcanvas.discardActiveObject();

  return {
    pMin: {
      x: selection.left - componentRoiPadding.left,
      y: selection.top - componentRoiPadding.top,
    },
    pMax: {
      x: selection.left + selection.width + componentRoiPadding.right,
      y: selection.top + selection.height + componentRoiPadding.bottom,
    },
    center: {
      x: center.x,
      y: center.y,
    }
  };
};

export const getRectInnerCenter = (rect) => {
  // fabric's top left includes the stroke width
  return {
    x: rect.left + (rect.left - rect.width * rect.scaleX + 1 - rect.strokeWidth) / 2 + rect.strokeWidth,
    y: rect.top + (rect.top - rect.height * rect.scaleY + 1 - rect.strokeWidth) / 2 + rect.strokeWidth,
  };
};

export const getPositionRotatedAroundPoint = (origPos, ang, referencePoint) => {
  const angleRad = fabric.util.degreesToRadians(ang);
  const cos = Math.cos(angleRad);
  const sin = Math.sin(angleRad);
  const dx = origPos.x - referencePoint.x;
  const dy = origPos.y - referencePoint.y;
  const tx = dx * (cos - 1) - dy * sin;
  const ty = dx * sin + dy * (cos - 1);

  return {
    x: origPos.x + tx,
    y: origPos.y + ty,
  };
};

export const rotatePoint = (point, angle, reference) => {
  if (angle === 0) return point; // No rotation needed
  const rad = (angle * Math.PI) / 180; // Convert degrees to radians

  // Translate point to origin
  const translatedX = point.x - reference.x;
  const translatedY = point.y - reference.y;

  // Apply rotation matrix
  const rotatedX = translatedX * Math.cos(rad) - translatedY * Math.sin(rad);
  const rotatedY = translatedX * Math.sin(rad) + translatedY * Math.cos(rad);

  // Translate back
  return {
    x: rotatedX + reference.x,
    y: rotatedY + reference.y
  };
};

export const rotateRectAroundRefPoint = (obj, objCenter, angle, objOriginalAng, refPoint, originalRectInnerDim) => {
  const nonRotatedCenter = rotatePoint(objCenter, -objOriginalAng, refPoint);
  const rotatedCenter = rotatePoint(nonRotatedCenter, angle, refPoint);

  obj.set({
    left: rotatedCenter.x - originalRectInnerDim.width / 2 - obj.strokeWidth,
    top: rotatedCenter.y - originalRectInnerDim.height / 2 - obj.strokeWidth,
    angle: 0,
  });

  obj.setCoords();
  obj.rotate(angle);
};

export const generateExtendedRoiByFeature = (feature, rectWidth) => {
  // if (_.isEmpty(_.intersection(extendedRoiRelatedLineItems, _.map(feature.line_item_params, l => l.agent_name)))) return;

  // const lineItemsNames = _.intersection(extendedRoiRelatedLineItems, _.keys(feature.line_item_params));
  // const lineItem = _.find(_.get(feature, 'line_item_params', {}), (v) => lineItemsNames.includes(v.agent_name));

  // if (!lineItem.active) return null;

  // const pMin = _.get(lineItem, 'param_roi.points[0]', { x: 0, y: 0 });
  // const pMax = _.get(lineItem, 'param_roi.points[1]', { x: 0, y: 0 });

  // // this position is based on the original image size
  // // hence we need to scale it to the current scene size
  // let newLeft = 0;
  // let newTop = 0;
  // let newWidth = 0;
  // let newHeight = 0;

  // newLeft = pMin.x;
  // newTop = pMin.y;
  // newWidth = pMax.x - pMin.x;
  // newHeight = pMax.y - pMin.y;

  // // backend will include the pMax point so...
  // newLeft -= rectWidth;
  // newTop -= rectWidth;
  // newWidth += rectWidth + 1;
  // newHeight += rectWidth + 1;

  // const rect = new fabric.Rect({
  //   left: newLeft,
  //   top: newTop,
  //   width: newWidth,
  //   height: newHeight,
  //   fill: 'transparent',
  //   stroke: getColorByStr(_.get(feature, 'feature_type')),
  //   strokeWidth: rectWidth,
  //   strokeUniform: true,
  //   evented: false,
  //   selectable: false,
  // });

  // if (_.get(lineItem, 'param_roi.type', 'aabb') === 'obb') {
  //   rect.rotate(_.get(lineItem, 'param_roi.angle', 0));
  //   rect.set('originalAng', _.get(lineItem, 'param_roi.angle', 0));
  // }
  // rect.set('featureObj', feature);

  // return rect;

  // TODO: remove this testing code
  const pMin = _.get(feature, 'roi.points[0]', { x: 0, y: 0 });
  const pMax = _.get(feature, 'roi.points[1]', { x: 0, y: 0 });

  // this position is based on the original image size
  // hence we need to scale it to the current scene size
  let newLeft = 0;
  let newTop = 0;
  let newWidth = 0;
  let newHeight = 0;

  newLeft = pMin.x - defaultNewFeatureExtRoiPadding.left - rectWidth * 2;
  newTop = pMin.y - defaultNewFeatureExtRoiPadding.top - rectWidth * 2;
  newWidth = pMax.x - pMin.x + rectWidth * 3 + defaultNewFeatureExtRoiPadding.left + defaultNewFeatureExtRoiPadding.right + 1;
  newHeight = pMax.y - pMin.y + rectWidth * 3 + defaultNewFeatureExtRoiPadding.top + defaultNewFeatureExtRoiPadding.bottom + 1;

  const rect = new fabric.Rect({
    left: newLeft,
    top: newTop,
    width: newWidth,
    height: newHeight,
    fill: 'transparent',
    stroke: getColorByStr(_.get(feature, 'feature_type')),
    strokeWidth: rectWidth,
    strokeUniform: true,
    evented: false,
    selectable: false,
  });

  rect.rotate(_.get(feature, 'roi.angle', 0));
  rect.set('originalAng', _.get(feature, 'roi.angle', 0));

  return rect;
};

export const generateFeatureDirection = (feature, rectWidth) => {
  // TODO: remove this test code, for now hard code the arrow direction
  const direction = 'right';

  const arrowStrokeWidth = _.round(rectWidth * 0.5, 0);
  
  const pMin = _.get(feature, 'roi.points[0]', { x: 0, y: 0 });
  const pMax = _.get(feature, 'roi.points[1]', { x: 0, y: 0 });

  // generate the triangle with height = 1/4 * feature height, width = 1/2 * feature width

  const width = pMax.x - pMin.x + 1;
  const height = pMax.y - pMin.y + 1;

  let triangle = null;

  let triangleCenter = null;
  let targetTopLeft = null;
  let originTopLeft = null;

  switch (direction) {
    case 'right':
      triangleCenter = {
        x: pMin.x + width/8,
        y: pMin.y + height/2,
      };
      targetTopLeft = {
        x: pMin.x + width/4,
        y: pMin.y + height/4,
      };
      originTopLeft = rotatePoint(targetTopLeft, -90, triangleCenter);
      triangle = new fabric.Triangle({
        left: originTopLeft.x,
        top: originTopLeft.y,
        width: height/2,
        height: width/4,
        fill: 'transparent',
        stroke: getColorByStr(_.get(feature, 'feature_type')),
        strokeWidth: arrowStrokeWidth,
        evented: false,
        selectable: false,
      });
      triangle.rotate(90);
      break;
    case 'left':

      break;
    default:
      break;
  }

  return {
    triangle,
  };
};

export const generateLeadSegmentation = (feature, rectWidth) => {
  // TODO: remove this test code for now hard code lead count and spacing leave lead width to be calculated(based on feature dimension)
  const leadCount = 5;
  const leadSpacing = 10;

  const pMin = _.get(feature, 'roi.points[0]', { x: 0, y: 0 });
  const pMax = _.get(feature, 'roi.points[1]', { x: 0, y: 0 });

  const width = pMax.x - pMin.x + 1;
  const height = pMax.y - pMin.y + 1;

  // width = leadCount * leadWidth + (leadCount - 1) * leadSpacing
  const leadWidth = (width - (leadCount - 1) * leadSpacing) / leadCount;
  // this leadWidth includes the rectWidth

  // generate all lead rects
  const leadRects = [];
  for (let i = 0; i < leadCount; i++) {
    const leadRect = new fabric.Rect({
      left: pMin.x + i * (leadWidth + leadSpacing) - newRectStrokeWidth,
      top: pMin.y,
      width: leadWidth + newRectStrokeWidth,
      height: height + newRectStrokeWidth,
      fill: 'transparent',
      stroke: 'white',
      strokeWidth: newRectStrokeWidth,
      strokeUniform: true,
      evented: false,
      selectable: false,
    });

    leadRects.push(leadRect);
  }

  return leadRects;
};

/**
 * Get the translation vector in a rotated coordinate system from point a to point b
 * @param {*} a
 * @param {*} b 
 * @param {*} angleInRadians 
 * @returns 
 */
export const getTranslationInRotatedSystem = (a, b, angleInRadians) => {
  // Step 1: Get translation vector in world coordinates
  const dx = b.x - a.x;
  const dy = b.y - a.y;

  // Step 2: Rotate this vector by -angle to convert to rotated coordinate system
  const cos = Math.cos(-angleInRadians);
  const sin = Math.sin(-angleInRadians);

  const translatedX = dx * cos - dy * sin;
  const translatedY = dx * sin + dy * cos;

  return { x: translatedX, y: translatedY };
};

export const generateAllAgentParamsDisplay = (
  featureObj,
  componentObj,
  featuresInComponent,
  mmToPixelRatio,
  t,
  isFeatureSelected,
  productObj,
  setSelectedAgentParam,
  fcanvasRef,
) => {
  if (_.isEmpty(_.get(featureObj, 'line_item_params', {}))) return [];

  const result = [];

  // 2d mounting polarity roi
  if (
    !_.isEmpty(_.get(featureObj, `line_item_params.${mountingInspection2D}.params.polarity_roi.param_roi`)) &&
    !_.isEmpty(_.get(featureObj, `line_item_params.${mountingInspection2D}.params.polarity_roi.param_roi.points`, [])) &&
    _.get(featureObj, `line_item_params.${mountingInspection2D}.params.polarity_roi.active`, false) &&
    _.get(featureObj, `line_item_params.${mountingInspection2D}.enabled`, false) &&
    featureObj.feature_type === mountingFeatureType
  ) {
    // 2d mounting polarity roi
    result.push(generateMounting2DPolarityRoi(
      _.get(featureObj, `line_item_params.${mountingInspection2D}.params.polarity_roi.param_roi`),
      false,
      featureObj,
      componentObj,
      null,
      null,
      null,
      setSelectedAgentParam,
      fcanvasRef,
    ));
  }

  if (
    isMounting2DMaskRoiEnabled(featureObj) &&
    featureObj.feature_type === mountingFeatureType
  ) {
    // 2d mounting mask roi
    result.push(generateMounting2DMaskRoi(
      _.get(featureObj, `line_item_params.${mountingInspection2D}.params.mask_roi.param_roi`),
      false,
      featureObj,
      componentObj,
      t,
      null,
      null,
      setSelectedAgentParam,
      fcanvasRef,
    ));
  }

  // 3d mounting extended roi
  if (
    !isAOI2DSMT &&
    _.get(featureObj, `line_item_params.${mountingInspection3D}.enabled`, false) &&
    isFeatureSelected &&
    featureObj.feature_type === mountingFeatureType
  ) {
    let isAllEdgeExtEnabled = true;
    for (const agentParamName of mounting3DExtendedRoiAgentParamNames) {
      if (!_.get(featureObj, `line_item_params.${mountingInspection3D}.params.${agentParamName}.active`, false)) {
        isAllEdgeExtEnabled = false;
        break;
      }
    }
    if (isAllEdgeExtEnabled) {
      // 3d mounting extended roi
      result.push(
        generateMounting3DExtendedRoi(
          featureObj,
          componentObj,
          false,
          _.get(featureObj, `line_item_params.${mountingInspection3D}.params.ext_left.param_int`, 0),
          _.get(featureObj, `line_item_params.${mountingInspection3D}.params.ext_right.param_int`, 0),
          _.get(featureObj, `line_item_params.${mountingInspection3D}.params.ext_top.param_int`, 0),
          _.get(featureObj, `line_item_params.${mountingInspection3D}.params.ext_bottom.param_int`, 0),
          null,
          null,
          null,
          null,
          null,
          null,
          setSelectedAgentParam,
          fcanvasRef,
        )
      );
    }
  }

  // 2 3d mounting background rois
  if (
    !isAOI2DSMT &&
    isMounting3DBackgroundRoi1Enabled(featureObj) &&
    isFeatureSelected &&
    featureObj.feature_type === mountingFeatureType
  ) {
    result.push(
      generateMounting3DBackgroundRoi(
        _.get(featureObj, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi`),
        false,
        featureObj,
        componentObj,
        t,
        null,
        null,
        setSelectedAgentParam,
        fcanvasRef,
        null,
        backgroundRoi1,
      )
    );
  }

  if (
    !isAOI2DSMT &&
    isMounting3DBackgroundRoi2Enabled(featureObj) &&
    isFeatureSelected &&
    featureObj.feature_type === mountingFeatureType
  ) {
    result.push(
      generateMounting3DBackgroundRoi(
        _.get(featureObj, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.param_roi`),
        false,
        featureObj,
        componentObj,
        t,
        null,
        null,
        setSelectedAgentParam,
        fcanvasRef,
        null,
        backgroundRoi2,
      )
    );
  }

  // lead segmentation display
  if (
    (isAOI2DSMT ? isLead2DEnabled(featureObj) : isLead3DEnabled(featureObj)) &&
    isFeatureSelected &&
    featureObj.feature_type === leadFeatureType
  ) {
    const srcObj = adaptLeadParamsForDisplay(featureObj);
    const leadSegRects = generateLead3DSegmentDisplay(
      srcObj,
      mmToPixelRatio,
      fcanvasRef,
      setSelectedAgentParam,
      false,
    );
    result.push(...leadSegRects);

    if (isAOI2DSMT) {
      const extTopRois = generateLead2DExtDisplay(
        featureObj,
        mmToPixelRatio,
        fcanvasRef,
        setSelectedAgentParam,
        false,
        null,
        null,
        null,
        t,
        null,
        null,
        null,
        extTop,
      );
      const extBotRois = generateLead2DExtDisplay(
        featureObj,
        mmToPixelRatio,
        fcanvasRef,
        setSelectedAgentParam,
        false,
        null,
        null,
        null,
        t,
        null,
        null,
        null,
        extBot,
      );
      result.push(...extTopRois, ...extBotRois);
    }
  }

  // solder
  if (
    !isAOI2DSMT &&
    isSolder3DEnabled(featureObj) &&
    isFeatureSelected &&
    featureObj.feature_type === solderFeatureType
  ) {
    const solderSegRects = generateSolder3DDisplay(
      featureObj,
      componentObj,
      false,
      t,
      null,
      null,
      null,
      null,
      null,
      setSelectedAgentParam,
      fcanvasRef,
    );
    result.push(
      ...solderSegRects,
    );
  }

  // ocr
  // if (
  //   isOCREnabled(featureObj) &&
  //   isFeatureSelected &&
  //   _.startsWith(featureObj.feature_type, '_text')
  // ) {
  //   const ocrArrow = generateOCRDisplay(
  //     featureObj,
  //   );
  //   result.push(ocrArrow);
  // }

  // attach is agent param obj
  for (const obj of result) {
    obj.set('isAgentParamObj', true);
  }

  return result;
};

export const isMountingFeature = (featureObj) => {
  return _.get(featureObj, `line_item_params.${mountingInspection2D}.enabled`, false) || _.get(featureObj, `line_item_params.${mountingInspection3D}.enabled`, false);
};

export const isLeadFeature = (featureObj) => {
  return _.get(featureObj, `line_item_params.${leadInspection2D}.enabled`, false) || _.get(featureObj, `line_item_params.${leadInspection3D}.enabled`, false);
};

export const generateLead3DSegmentDisplay = (
  featureObj,
  mmToPixelRatio,
  fcanvasRef,
  setSelectedAgentParam,
  interactive,
  selectedAgentParam,
  updateFeature,
  updateAllFeaturesState,
  t,
  allFeatures,
  componentObj,
  updateComponent,
  refetchAllComponents,
  updateGroupAgentParams,
) => {
  // we assume lead ang is point outward which is the direction of the extended roi
  const result = [];
  let featureCenter = getComponentCenterByRoiDtoObj(_.get(featureObj, 'roi', {}));
  const featureAngle = _.get(featureObj, 'roi.angle', 0);
  // featureCenter = rotatePoint(featureCenter, _.get(componentObj, 'shape.angle', 0), getComponentCenterByRoiDtoObj(_.get(componentObj, 'shape', {})));

  // check the lead angle - feature angle
  // ex. abs(delta) < 45 or 225 > abs(delta) > 135 then we should use x axis' side to segment the lead else y axis' side
  // else we should use y axis' side to segment the lead
  // const leadFeatureAngleDelta = Math.abs(_.get(featureObj, 'roi.angle', 0) - leadAngle);

  // const isXSide = ((leadFeatureAngleDelta < 45) || (leadFeatureAngleDelta >= 135 && leadFeatureAngleDelta <= 225));
  const isXSide = true;

  const leadCount = _.get(featureObj, `line_item_params.${leadInspection3D}.params.lead_count.param_int.value`, 0);
  // const leadSpacingMM = _.get(featureObj, `line_item_params.${leadInspection3D}.params.bridge_width_mm.param_float.value`, 0);
  const leadWidthMM = _.get(featureObj, `line_item_params.${leadInspection3D}.params.lead_width_mm.param_float.value`, 0);

  // lead spacing in mm but we need it in pixel
  // const leadSpacing = await getMMDistanceInPixel(
  //   leadSpacingMM,
  //   physicalCoordMap,
  //   _.get(componentObj, 'depth_map_uri', ''),
  //   t
  // );
  // const leadWidth = await getMMDistanceInPixel(
  //   leadWidthMM,
  //   physicalCoordMap,
  //   _.get(productObj, 'inspectables[0].depth_map_uri', ''),
  //   t,
  // );
  const leadWidth = leadWidthMM * mmToPixelRatio;

  const pMin = _.get(featureObj, 'roi.points[0]', { x: 0, y: 0 });
  const pMax = _.get(featureObj, 'roi.points[1]', { x: 0, y: 0 });

  const width = pMax.x - pMin.x + 1;
  const height = pMax.y - pMin.y + 1;

  // let leadWidth;
  // if (isXSide) {
  //   // generate lead segmentation based on x axis
  //   leadWidth = (width - (leadCount - 1) * leadSpacing) / leadCount;
  // } else {
  //   leadWidth = (height - (leadCount - 1) * leadSpacing) / leadCount;
  // }

  let leadSpacing;
  if (isXSide) {
    leadSpacing = (width - leadCount * leadWidth) / (leadCount - 1);
  } else {
    leadSpacing = (height - leadCount * leadWidth) / (leadCount - 1);
  }

  // we need pmin pmax rotated around component center
  const rotatedPMin = {
    x: featureCenter.x - width / 2,
    y: featureCenter.y - height / 2,
  };
  // const rotatedPMax = {
  //   x: rotatedFeatureCenter.x + width / 2,
  //   y: rotatedFeatureCenter.y + height / 2,
  // };

  // we only have ext_bottom in this case
  const extBottom = _.get(featureObj, `line_item_params.${leadInspection3D}.params.${lead3DExtTop}.param_int.value`, 0);

  for (let i = 0; i < leadCount; i++) {
    let leadRect;
    let extRoi;
    if (isXSide) {
      let curLeadCenter = {
        x: rotatedPMin.x + i * (leadWidth + leadSpacing) + leadWidth / 2,
        y: rotatedPMin.y + height / 2,
      };
      curLeadCenter = rotatePoint(curLeadCenter, featureAngle, featureCenter);
      leadRect = new fabric.Rect({
        left: curLeadCenter.x - leadWidth / 2 - newRectStrokeWidth,
        top: curLeadCenter.y - height / 2 - newRectStrokeWidth,
        width: leadWidth + newRectStrokeWidth,
        height: height + newRectStrokeWidth,
        fill: 'transparent',
        stroke: getColorByStr(`${leadInspection3D}.${leadSegmentationRects}`),
        strokeWidth: newRectStrokeWidth,
        strokeUniform: true,
        evented: false,
        selectable: false,
      });
      
      leadRect.rotate(featureAngle);

      if (extBottom > 0) {
        let curExtRoiCenter = {
          x: rotatedPMin.x + i * (leadWidth + leadSpacing) + leadWidth / 2,
          y: rotatedPMin.y + height / 2 - extBottom/2,
        };
        curExtRoiCenter = rotatePoint(curExtRoiCenter, featureAngle, featureCenter);
        // curExtRoiCenter = rotatePoint(curExtRoiCenter, featureAngle, curLeadCenter);

        extRoi = new fabric.Rect({
          left: curExtRoiCenter.x - leadWidth / 2 - newRectStrokeWidth,
          top: curExtRoiCenter.y - height / 2 - newRectStrokeWidth - extBottom/2,
          width: leadWidth + newRectStrokeWidth,
          height: height + newRectStrokeWidth + extBottom,
          fill: 'transparent',
          stroke: getColorByStr(`${leadInspection3D}.${leadSegmentationRects}.${extendedRoi}`),
          strokeWidth: newRectStrokeWidth,
          strokeUniform: true,
          evented: true,
          selectable: false,
        });
        
        if (!interactive) {
          extRoi.set('evented', true);
          extRoi.set('selectable', true);
          extRoi.hoverCursor = 'pointer';

          extRoi.on('mouseover', () => {
            extRoi.set('stroke', '#FEB617');
            fcanvasRef.current.renderAll();
          });

          extRoi.on('mouseout', () => {
            extRoi.set('stroke', getColorByStr(`${leadInspection3D}.${leadSegmentationRects}.${extendedRoi}`));
            fcanvasRef.current.renderAll();
          });

          extRoi.on('mousedown', () => {
            setSelectedAgentParam(`${leadInspection3D}.${leadSegmentationRects}.${extendedRoi}.${i}`); // i is the index of the lead
          });
        } else if (interactive) {
          const targetLeadIndex = Number(_.split(selectedAgentParam, '.').pop());
          if (targetLeadIndex === i) {
            extRoi.set('evented', true);
            extRoi.set('selectable', true);
            // only allow scale top
            extRoi.setControlsVisibility({
              mtr: false,
              mt: true,
              mb: false,
              ml: false,
              mr: false,
              bl: false,
              br: false,
              tl: false,
              tr: false,
            });

            extRoi.onDeselect = (opt) => {
              if (!extRoi) return;
              if (_.isUndefined(opt.e)) return;
              setSelectedAgentParam(null);
            };

            extRoi.lockMovementX = true;
            extRoi.lockMovementY = true;
            extRoi.on('modified', () => {
              const newExtTop = _.min([_.round(extRoi.getScaledHeight() - height - newRectStrokeWidth, 0), _.get(
                featureObj,
                `line_item_params.${leadInspection3D}.params.${lead3DExtTop}.param_int.max`,
                0
              )]);

              if (newExtTop < 0) {
                // revert
                aoiAlert(t('notification.error.extendedRoiCannotBeNegative'), ALERT_TYPES.COMMON_ERROR);
                return;
              }

              const payload = {
                ...featureObj,
                line_item_params: {
                  ...featureObj.line_item_params,
                  [leadInspection3D]: {
                    ...featureObj.line_item_params[leadInspection3D],
                    params: {
                      ...featureObj.line_item_params[leadInspection3D].params,
                      [lead3DExtTop]: {
                        ...featureObj.line_item_params[leadInspection3D].params[lead3DExtTop],
                        param_int: {
                          ...featureObj.line_item_params[leadInspection3D].params[lead3DExtTop].param_int,
                          value: newExtTop,
                        },
                      },
                    },
                  },
                },
              };

              let cPayload;

              if (_.isInteger(featureObj.group_id)) {
                // also need to update component
                let relatedFeatures = _.filter(allFeatures, (f) => {
                  return f.group_id === featureObj.group_id
                });
                // replace the feature in the related features
                relatedFeatures = _.map(relatedFeatures, (f) => {
                  if (f.feature_id === featureObj.feature_id) {
                    return payload;
                  }
                  if (f.feature_type === featureObj.feature_type) {
                    return {
                      ...f,
                      line_item_params: payload.line_item_params,
                    };
                  }
                  return f;
                });

                const newComponentInfo = getComponentRectInfoByFeatures(relatedFeatures, componentObj);

                cPayload = {
                  ...componentObj,
                  shape: {
                    ...componentObj.shape,
                    points: [
                      newComponentInfo.pMin,
                      newComponentInfo.pMax,
                    ]
                  },
                  // all: true,
                };

                delete cPayload['color_map_uri'];
                delete cPayload['depth_map_uri'];
                delete cPayload['created_at'];
                delete cPayload['modified_at'];
                delete cPayload['can_group_by_package_no'];
                delete cPayload['can_group_by_part_no'];
                delete cPayload['array_index'];
                delete cPayload['cloned'];
                delete cPayload['designator'];
                delete cPayload['variation_for'];
              }

              const submit = async (payload, cPayload) => {
                if (cPayload) {
                  const res1 = await updateComponent({
                    body: cPayload,
                    params: { allComponents: true },
                  });

                  if (res1.error) {
                    console.error('Failed to update component:', res1.error);
                    aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }

                  const res = await updateGroupAgentParams({
                    line_item_params: removeProfileHeightFromPayload(payload).line_item_params,
                    product_id: payload.product_id,
                    step: 0,
                    feature_type: payload.feature_type,
                    component_id: payload.group_id,
                  });

                  if (res.error) {
                    console.error('Failed to update feature:', res.error);
                    aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }

                  await refetchAllComponents();

                  if (!_.isEmpty(res.data)) {
                    await updateAllFeaturesState(res.data.feature_ids, 'updateGroupParam', res.data.line_item_params);
                  }
                } else {
                  const res = await updateFeature({ body: payload, params: { allComponents: _.isInteger(payload.group_id) } });

                  if (res.error) {
                    console.error('Failed to update feature:', res.error);
                    aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }

                  if (cPayload && !_.isEmpty(cPayload)) await refetchAllComponents();

                  // await updateAllFeaturesState([payload.feature_id], 'update', [payload]);
                  if (!_.isEmpty(res.data)) {
                    await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
                  }
                }
              };

              submit(payload, cPayload);
            });
          } else {
            extRoi.set('evented', false);
          }
        }

        extRoi.rotate(featureAngle);
        extRoi.set('agentParamLabel', `${leadInspection3D}.${leadSegmentationRects}.${extendedRoi}`);
        if (interactive) {
          const targetLeadIndex = Number(_.split(selectedAgentParam, '.').pop());
          if (targetLeadIndex === i) {
            // insert and make it the first one
            result.unshift(extRoi);
          } else {
            result.push(extRoi);
          }
        } else {
          result.push(extRoi);
        }
      }
    } else {
      let curLeadCenter = {
        x: rotatedPMin.x + width / 2,
        y: rotatedPMin.y + i * (leadWidth + leadSpacing) + leadWidth / 2,
      };
      curLeadCenter = rotatePoint(curLeadCenter, featureAngle, featureCenter);
      leadRect = new fabric.Rect({
        left: curLeadCenter.x - leadWidth / 2 - newRectStrokeWidth,
        top: curLeadCenter.y - width / 2 - newRectStrokeWidth,
        width: leadWidth + newRectStrokeWidth,
        height: width + newRectStrokeWidth,
        fill: 'transparent',
        stroke: getColorByStr(`${leadInspection3D}.${leadSegmentationRects}`),
        strokeWidth: newRectStrokeWidth,
        strokeUniform: true,
        evented: false,
        selectable: false,
      });

      leadRect.rotate(featureAngle);

      if (extBottom > 0) {
        let curExtRoiCenter = {
          x: rotatedPMin.x + width / 2,
          y: rotatedPMin.y + i * (leadWidth + leadSpacing) + leadWidth / 2 - extBottom /2,
        };
        curExtRoiCenter = rotatePoint(curExtRoiCenter, featureAngle, featureCenter);
        curExtRoiCenter = rotatePoint(curExtRoiCenter, featureAngle, curLeadCenter);
        extRoi = new fabric.Rect({
          left: curExtRoiCenter.x - leadWidth / 2 - newRectStrokeWidth,
          top: curLeadCenter.y - width / 2 - newRectStrokeWidth - extBottom/2,
          width: leadWidth + newRectStrokeWidth,
          height: width + newRectStrokeWidth + extBottom,
          fill: 'transparent',
          stroke: getColorByStr(`${leadInspection3D}.${leadSegmentationRects}.${extendedRoi}`),
          strokeWidth: newRectStrokeWidth,
          strokeUniform: true,
          evented: false,
          selectable: false,
        });

        extRoi.rotate(featureAngle);
        extRoi.set('agentParamLabel', `${leadInspection3D}.${leadSegmentationRects}.${extendedRoi}`);
        result.push(extRoi);
      }
    }
    leadRect.set('agentParamLabel', `${leadInspection3D}.${leadSegmentationRects}`);

    // lead angle direction arrow
    const leadAngArrow = generateFabricArrow({
      angleDeg: featureAngle,
      center: leadRect.getCenterPoint(),
      dimension: {
        height: leadWidth/2,
        width: leadWidth/2,
      },
      color: getColorByStr(`${leadInspection3D}.${directionArrow}`),
    });
    leadAngArrow.set('agentParamLabel', `${leadInspection3D}.${directionArrow}`);
    leadAngArrow.set('evented', false);
    leadAngArrow.set('selectable', false);
    result.push(leadRect);
    result.push(leadAngArrow);
  }

  return result;
};

export const generateLead2DExtDisplay = (
  featureObj,
  mmToPixelRatio,
  fcanvasRef,
  setSelectedAgentParam,
  interactive,
  selectedAgentParam,
  updateFeature,
  updateAllFeaturesState,
  t,
  allFeatures,
  componentObj,
  updateGroupAgentParams,
  extName,
) => {
  const result = [];
  const featureCenter = getComponentCenterByRoiDtoObj(_.get(featureObj, 'roi', {}));
  const featureAngle = _.get(featureObj, 'roi.angle', 0);

  const leadCount = _.get(featureObj, `line_item_params.${leadInspection2D}.params.lead_count.param_int.value`, 0);
  const leadWidthMM = _.get(featureObj, `line_item_params.${leadInspection2D}.params.lead_width_mm.param_float.value`, 0);
  const leadWidth = leadWidthMM * mmToPixelRatio;

  const pMin = _.get(featureObj, 'roi.points[0]', { x: 0, y: 0 });
  const pMax = _.get(featureObj, 'roi.points[1]', { x: 0, y: 0 });
  const width = pMax.x - pMin.x + 1;
  const height = pMax.y - pMin.y + 1;

  const leadSpacing = (width - leadCount * leadWidth) / (leadCount - 1);

  const rotatedPMin = {
    x: featureCenter.x - width / 2,
    y: featureCenter.y - height / 2,
  };

  const extVal = _.get(featureObj, `line_item_params.${leadInspection2D}.params.${extName}.param_int.value`, 0);

  if (extVal <= 0) return result;

  for (let i = 0; i < leadCount; i++) {
    const leadTopLeft = {
      x: rotatedPMin.x + i * (leadWidth + leadSpacing),
      y: rotatedPMin.y,
    };
    const leadBottomLeft = {
      x: rotatedPMin.x + i * (leadWidth + leadSpacing),
      y: rotatedPMin.y + height,
    };

    let roiCenter;
    if (extName === extTop) {
      roiCenter = {
        x: leadTopLeft.x + leadWidth / 2,
        y: leadTopLeft.y + extVal / 2,
      };
    } else {
      roiCenter = {
        x: leadBottomLeft.x + leadWidth / 2,
        y: leadBottomLeft.y - extVal / 2,
      };
    }

    roiCenter = rotatePoint(roiCenter, featureAngle, featureCenter);

    const rect = new fabric.Rect({
      left: roiCenter.x - leadWidth / 2 - newRectStrokeWidth,
      top: roiCenter.y - extVal / 2 - newRectStrokeWidth,
      width: leadWidth + newRectStrokeWidth,
      height: extVal + newRectStrokeWidth,
      fill: 'transparent',
      stroke: getColorByStr(`${leadInspection2D}.${extName}`),
      strokeWidth: newRectStrokeWidth,
      strokeUniform: true,
      evented: false,
      selectable: false,
    });

    rect.rotate(featureAngle);
    rect.set('agentParamLabel', `${leadInspection2D}.${extName}`);

    if (!interactive) {
      rect.set('evented', true);
      rect.set('selectable', true);
      rect.setControlsVisibility({ mt: false, mb: false, ml: false, mr: false, bl: false, br: false, tl: false, tr: false, mtr: false });
      rect.hoverCursor = 'pointer';
      rect.on('mousedown', () => {
        setSelectedAgentParam(`${leadInspection2D}.${extName}.${i}`);
      });
      rect.on('mouseover', () => {
        rect.set('stroke', '#FEB617');
        fcanvasRef.current.renderAll();
      });
      rect.on('mouseout', () => {
        rect.set('stroke', getColorByStr(`${leadInspection2D}.${extName}`));
        fcanvasRef.current.renderAll();
      });
      result.push(rect);
    } else {
      const targetIndex = Number(_.split(selectedAgentParam, '.').pop());
      if (targetIndex === i) {
        rect.set('evented', true);
        rect.set('selectable', true);
        rect.setControlsVisibility({
          mtr: false,
          mt: extName === extBot,
          mb: extName === extTop,
          ml: false,
          mr: false,
          bl: false,
          br: false,
          tl: false,
          tr: false,
        });
        rect.lockMovementX = true;
        rect.lockMovementY = true;
        rect.onDeselect = (opt) => {
          if (!rect) return;
          if (_.isUndefined(opt.e)) return;
          setSelectedAgentParam(null);
        };
        rect.on('modified', async () => {
          let newVal = _.round(rect.getScaledHeight() - newRectStrokeWidth, 0);
          const featureHeight = height;
          newVal = _.min([
            newVal,
            featureHeight,
            _.get(featureObj, `line_item_params.${leadInspection2D}.params.${extName}.param_int.max`, newVal),
          ]);
          const payload = {
            ...featureObj,
            line_item_params: {
              ...featureObj.line_item_params,
              [leadInspection2D]: {
                ...featureObj.line_item_params[leadInspection2D],
                params: {
                  ...featureObj.line_item_params[leadInspection2D].params,
                  [extName]: {
                    ...featureObj.line_item_params[leadInspection2D].params[extName],
                    param_int: {
                      ...featureObj.line_item_params[leadInspection2D].params[extName].param_int,
                      value: newVal,
                    },
                  },
                },
              },
            },
          };

          let res;
          if (!_.isInteger(payload.group_id)) {
            res = await updateFeature({ body: payload, params: { allComponents: _.isInteger(payload.group_id) } });
          } else {
            res = await updateGroupAgentParams({
              line_item_params: removeProfileHeightFromPayload(payload).line_item_params,
              product_id: payload.product_id,
              step: 0,
              feature_type: payload.feature_type,
              component_id: payload.group_id,
            });
          }

          if (res && res.error) {
            console.error('Failed to update feature:', res.error);
            aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
            return;
          }

          if (res && !_.isEmpty(res.data)) {
            if (_.isInteger(payload.group_id)) {
              await updateAllFeaturesState(res.data.feature_ids, 'updateGroupParam', res.data.line_item_params);
            } else {
              await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
            }
          }
        });
        result.unshift(rect);
      } else {
        result.push(rect);
      }
    }
  }

  return result;
};

export const generateSolder3DExtendedRoi = (
  featureObj,
  componentObj,
  interactive,
  // below are required for interactive
  t,
  updateFeature,
  refetchAllFeatures,
  features,
  updateComponent,
  refetchAllComponents,
  setSelectedAgentParam,
  updateAllFeaturesState,
  updateGroupAgentParams,
) => {
  const {
    rect: extendedRoiRect,
    nonRotatedInnerDimension,
  } = generateExtendedRoi(
    // _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_left.param_int`),
    0,
    _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_top.param_int`),
    // _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_right.param_int`),
    0,
    _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_bottom.param_int`),
    featureObj,
    componentObj,
    solderInspection3D,
  );

  if (interactive) {
    extendedRoiRect.set('evented', true);
    // extendedRoiRect.set('selectable', true);
    // attach event
    extendedRoiRect.onDeselect = (opt) => {
      if (!extendedRoiRect) return;
      if (_.isUndefined(opt.e)) return;
      setSelectedAgentParam(null);
    };

    attachModifiedEventHandleForExtendedRoi(
      extendedRoiRect,
      componentObj,
      nonRotatedInnerDimension,
      featureObj,
      solderInspection3D,
      setSelectedAgentParam,
      refetchAllFeatures,
      refetchAllComponents,
      updateFeature,
      updateComponent,
      features,
      t,
      updateAllFeaturesState,
      updateGroupAgentParams,
    );

    // we don't allow rotation, left right scaling and translation
    extendedRoiRect.setControlsVisibility({
      mtr: false,
      // mt: false,
      // mb: false,
      ml: false,
      mr: false,
      bl: false,
      br: false,
      tl: false,
      tr: false,
    });
    extendedRoiRect.lockMovementX = true;
    extendedRoiRect.lockMovementY = true;
    extendedRoiRect.hoverCursor = 'default';
  } else {
    extendedRoiRect.set('evented', false);
    extendedRoiRect.set('selectable', false);
    extendedRoiRect.setControlsVisibility({
      mt: false,
      mb: false,
      ml: false,
      mr: false,
      bl: false,
      br: false,
      tl: false,
      tr: false,
      mtr: false,
    }); 
  }

  return extendedRoiRect;
};

export const generateSolder3DProfileRoi = (
  featureObj,
  componentObj,
  interactive,
  // below are required for interactive
  t,
  updateFeature,
  refetchAllFeatures,
  features,
  updateComponent,
  refetchAllComponents,
  setSelectedAgentParam,
  updateAllFeaturesState,
  updateGroupAgentParams,
) => {
  const {
    rect: profileRoiRect,
  } = generateProfileRoi(
    featureObj,
    solderInspection3D,
    componentObj,
  );

  if (interactive) {
    profileRoiRect.set('evented', true);
    // profileRoiRect.set('selectable', true);
    // attach event
    profileRoiRect.onDeselect = (opt) => {
      if (!profileRoiRect) return;
      if (_.isUndefined(opt.e)) return;
      setSelectedAgentParam(null);
    };

    attachModifiedEventForProfileRoi(
      profileRoiRect,
      componentObj,
      featureObj,
      solderInspection3D,
      updateFeature,
      refetchAllFeatures,
      t,
      updateAllFeaturesState,
      updateGroupAgentParams,
    );

    // we only allow scaling x for profile roi
    profileRoiRect.setControlsVisibility({
      mtr: false,
      mt: false,
      mb: false,
      tr: false,
      bl: false,
      br: false,
      tl: false,
    });
    profileRoiRect.lockMovementX = true;
    profileRoiRect.lockMovementY = true;
    profileRoiRect.hoverCursor = 'default';
  } else {
    profileRoiRect.set('evented', false);
    profileRoiRect.set('selectable', false);
    profileRoiRect.setControlsVisibility({
      mt: false,
      mb: false,
      ml: false,
      mr: false,
      bl: false,
      br: false,
      tl: false,
      tr: false,
      mtr: false,
    });
  }

  return profileRoiRect;
};

export const generateSolder3DDisplay = (
  featureObj,
  componentObj,
  interactive,
  // below are required for interactive
  t,
  updateFeature,
  refetchAllFeatures,
  features,
  updateComponent,
  refetchAllComponents,
  setSelectedAgentParam,
  fcanvasRef,
) => {
  // have expect an extended roi and a profile roi to display in solder 3d agent
  const {
    rect: extendedRoiRect,
    nonRotatedInnerDimension,
  } = generateExtendedRoi(
    _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_left.param_int`),
    _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_top.param_int`),
    _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_right.param_int`),
    _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_bottom.param_int`),
    featureObj,
    componentObj,
    solderInspection3D,
  );

  extendedRoiRect.set('agentParamLabel', `${solderInspection3D}.${extendedRoi}`);

  const {
    rect: profileRoiRect,
  } = generateProfileRoi(
    featureObj,
    solderInspection3D,
    componentObj,
  );

  profileRoiRect.set('agentParamLabel', `${solderInspection3D}.${profileRoi}`);

  if (interactive) {
    extendedRoiRect.set('evented', true);
    // extendedRoiRect.set('selectable', true);
    // attach event
    extendedRoiRect.onDeselect = (opt) => {
      if (!extendedRoiRect) return;
      if (_.isUndefined(opt.e)) return;
      setSelectedAgentParam(null);
    };

    attachModifiedEventHandleForExtendedRoi(
      extendedRoiRect,
      componentObj,
      nonRotatedInnerDimension,
      featureObj,
      solderInspection3D,
      setSelectedAgentParam,
      refetchAllFeatures,
      refetchAllComponents,
      updateFeature,
      updateComponent,
      features,
      t,
    );

    // we don't allow rotation and translation
    extendedRoiRect.setControlsVisibility({
      mtr: false,
    });
    extendedRoiRect.lockMovementX = true;
    extendedRoiRect.lockMovementY = true;
    extendedRoiRect.hoverCursor = 'default';

    profileRoiRect.set('evented', true);
    profileRoiRect.set('selectable', true);
    // attach event
    profileRoiRect.onDeselect = (opt) => {
      if (!profileRoiRect) return;
      if (_.isUndefined(opt.e)) return;
      setSelectedAgentParam(null);
    };

    attachModifiedEventForProfileRoi(
      profileRoiRect,
      componentObj,
      featureObj,
      solderInspection3D,
      updateFeature,
      refetchAllFeatures,
      t,
    );

    // we only allow translation for profile roi
    profileRoiRect.setControlsVisibility({
      mtr: false,
      mt: false,
      mb: false,
      ml: false,
      mr: false,
    });
  } else {
    extendedRoiRect.set('evented', true);
    extendedRoiRect.set('selectable', true);
    extendedRoiRect.setControlsVisibility({
      mt: false,
      mb: false,
      ml: false,
      mr: false,
      bl: false,
      br: false,
      tl: false,
      tr: false,
      mtr: false,
    });

    extendedRoiRect.set('hoverCursor', 'pointer');
    extendedRoiRect.on('mousedown', (opt) => {
      if (opt.e.button === 0) {
        setSelectedAgentParam(`${solderInspection3D}.${extendedRoi}`);
      }
    });
    extendedRoiRect.on('mouseover', () => {
      extendedRoiRect.set('stroke', '#FEB617');
      fcanvasRef.current.renderAll();
    });
    extendedRoiRect.on('mouseout', () => {
      extendedRoiRect.set('stroke', getColorByStr(`${solderInspection3D}.${extendedRoi}`));
      fcanvasRef.current.renderAll();
    });

    profileRoiRect.set('evented', true);
    profileRoiRect.set('selectable', true);
    profileRoiRect.setControlsVisibility({
      mt: false,
      mb: false,
      ml: false,
      mr: false,
      bl: false,
      br: false,
      tl: false,
      tr: false,
      mtr: false,
    });

    profileRoiRect.set('hoverCursor', 'pointer');
    profileRoiRect.on('mousedown', (opt) => {
      if (opt.e.button === 0) {
        setSelectedAgentParam(`${solderInspection3D}.${profileRoi}`);
      }
    });
    profileRoiRect.on('mouseover', () => {
      profileRoiRect.set('stroke', '#FEB617');
      fcanvasRef.current.renderAll();
    });
    profileRoiRect.on('mouseout', () => {
      profileRoiRect.set('stroke', getColorByStr(`${solderInspection3D}.${profileRoi}`));
      fcanvasRef.current.renderAll();
    });
  }

  const solderAngleArrow = generateFabricArrow({
    // angleDeg: _.get(featureObj, `line_item_params.${solderInspection3D}.params.solder_angle.param_int.value`, 0),
    angleDeg: _.get(featureObj, 'roi.angle', 0),
    center: profileRoiRect.getCenterPoint(),
    color: getColorByStr(`${solderInspection3D}.${directionArrow}`),
    dimension: {
      width: profileRoiRect.width / 2,
      height: profileRoiRect.height / 2,
    }
  });

  solderAngleArrow.set('agentParamLabel', `${solderInspection3D}.${directionArrow}`);

  return [extendedRoiRect, profileRoiRect, solderAngleArrow];
};

export const generateOCRDisplay = (
  featureObj,
) => {
  // just a feature angle direction arrow
  // we want to keep the arrow height / arrow width ratio of the arrow to be 2:1
  // arrow long side < feature long side/4, arrow short side < feature short side

  const featureDimension = {
    width: (_.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0)+1),
    height: (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0)+1),
  };

  const isWidthLongSide = featureDimension.width > featureDimension.height;
  let arrowLongSide = isWidthLongSide ? featureDimension.width / 4 : featureDimension.height / 4;
  let arrowShortSide = isWidthLongSide ? featureDimension.height : featureDimension.width;
  if (arrowLongSide / 2 > arrowShortSide) {
    arrowLongSide = arrowShortSide * 2;
  } else {
    arrowShortSide = arrowLongSide / 2;
  }

  const featureCenter = getComponentCenterByRoiDtoObj(_.get(featureObj, 'roi', {}));

  // let arrowCenter = {
  //   x: featureCenter.x,
  //   y: _.get(featureObj, 'roi.points[1].y') - (isWidthLongSide ? arrowShortSide / 2 : arrowLongSide / 2),
  // };
  let arrowCenter = {
    x: _.get(featureObj, 'roi.points[0].x') + (isWidthLongSide ? arrowShortSide / 2 : arrowLongSide / 2),
    y: featureCenter.y,
  };
  arrowCenter = rotatePoint(arrowCenter, _.get(featureObj, 'roi.angle', 0), featureCenter);

  // console.log('arrowCenter', arrowCenter);
  // console.log('arrowLongSide', arrowLongSide);
  // console.log('arrowShortSide', arrowShortSide);

  const OCRAngleArrow = generateFabricArrow({
    angleDeg: _.get(featureObj, 'roi.angle', 0)+90,
    center: arrowCenter,
    color: getColorByStr(`${textVerification}.${directionArrow}`),
    dimension: {
      width: isWidthLongSide ? arrowLongSide : arrowShortSide,
      height: isWidthLongSide ? arrowShortSide : arrowLongSide,
    },
    isFilled: false,
  });

  OCRAngleArrow.set('agentParamLabel', `${textVerification}.${directionArrow}`);

  return OCRAngleArrow;
};

export const generateMounting3DExtendedRoi = (
  featureObj,
  componentObj,
  interactive,
  extLeft,
  extRight,
  extTop,
  extBottom,
  // below are required for interactive
  t,
  updateFeature,
  refetchAllFeatures,
  features,
  updateComponent,
  refetchAllComponents,
  setSelectedAgentParam,
  fcanvasRef,
  updateAllFeaturesState,
  updateGroupAgentParams,
) => {
  const {
    rect,
    nonRotatedInnerDimension,
  } = generateExtendedRoi(
    extLeft,
    extTop,
    extRight,
    extBottom,
    featureObj,
    componentObj,
    mountingInspection3D,
  );

  if (interactive) {
    rect.set('evented', true);
    rect.set('selectable', true);

    rect.onDeselect = (opt) => {
      if (!rect) return;
      if (_.isUndefined(opt.e)) return;
      setSelectedAgentParam(null);
    };

    attachModifiedEventHandleForExtendedRoi(
      rect,
      componentObj,
      nonRotatedInnerDimension,
      featureObj,
      mountingInspection3D,
      setSelectedAgentParam,
      refetchAllFeatures,
      refetchAllComponents,
      updateFeature,
      updateComponent,
      features,
      t,
      updateAllFeaturesState,
      updateGroupAgentParams,
    );

    // we don't allow rotation
    rect.setControlsVisibility({
      mtr: false,
    });

  } else {
    rect.set('evented', true);
    rect.set('selectable', true);
    rect.setControlsVisibility({
      mt: false,
      mb: false,
      ml: false,
      mr: false,
      bl: false,
      br: false,
      tl: false,
      tr: false,
      mtr: false,
    });
    rect.set('hoverCursor', 'pointer');
    rect.on('mousedown', (opt) => {
      if (opt.e.button === 0) {
        setSelectedAgentParam(`${mountingInspection3D}.${extendedRoi}`);
      }
    });
    rect.on('mouseover', () => {
      rect.set('stroke', '#FEB617');
      fcanvasRef.current.renderAll();
    });
    rect.on('mouseout', () => {
      rect.set('stroke', getColorByStr(`${mountingInspection3D}.${extendedRoi}`));
      fcanvasRef.current.renderAll();
    });
  }

  return rect;
};

export const generateSelectedAgentParamDisplayObj = (
  featureObj,
  componentObj,
  selectedAgentParam,
  t,
  updateFeature,
  refetchAllFeatures,
  setSelectedAgentParam,
  allFeatures,
  updateComponent,
  refetchAllComponents,
  fcanvasRef,
  updateAllFeaturesState,
  mmToPixelRatio,
  updateGroupAgentParams,
) => {
  if (_.isEmpty(selectedAgentParam)) return null;

  let result;

  if (selectedAgentParam === `${mountingInspection2D}.${polarityRoi}`) {
    result = [generateMounting2DPolarityRoi(
      _.get(featureObj, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi`),
      true,
      featureObj,
      componentObj,
      t,
      updateFeature,
      refetchAllFeatures,
      setSelectedAgentParam,
      fcanvasRef,
      updateAllFeaturesState,
      updateGroupAgentParams,
    )];
  }

  if (selectedAgentParam === `${mountingInspection2D}.${maskRoi}`) {
    result = [generateMounting2DMaskRoi(
      _.get(featureObj, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi`),
      true,
      featureObj,
      componentObj,
      t,
      updateFeature,
      refetchAllFeatures,
      setSelectedAgentParam,
      fcanvasRef,
      updateAllFeaturesState,
      updateGroupAgentParams,
    )];
  }

  if (selectedAgentParam === `${mountingInspection3D}.${extendedRoi}`) {
    result = [generateMounting3DExtendedRoi(
      featureObj,
      componentObj,
      true,
      _.get(featureObj, `line_item_params.${mountingInspection3D}.params.ext_left.param_int`),
      _.get(featureObj, `line_item_params.${mountingInspection3D}.params.ext_right.param_int`),
      _.get(featureObj, `line_item_params.${mountingInspection3D}.params.ext_top.param_int`),
      _.get(featureObj, `line_item_params.${mountingInspection3D}.params.ext_bottom.param_int`),
      t,
      updateFeature,
      refetchAllFeatures,
      allFeatures,
      updateComponent,
      refetchAllComponents,
      setSelectedAgentParam,
      fcanvasRef,
      updateAllFeaturesState,
      updateGroupAgentParams,
    )];
  }

  if (selectedAgentParam === `${mountingInspection3D}.${backgroundRoi1}`) {
    result = [generateMounting3DBackgroundRoi(
      _.get(featureObj, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi`),
      true,
      featureObj,
      componentObj,
      t,
      updateFeature,
      refetchAllFeatures,
      setSelectedAgentParam,
      fcanvasRef,
      updateAllFeaturesState,
      backgroundRoi1,
      updateComponent,
      allFeatures,
      refetchAllComponents,
      updateGroupAgentParams,
    )];
  }

  if (selectedAgentParam === `${mountingInspection3D}.${backgroundRoi2}`) {
    result = [generateMounting3DBackgroundRoi(
      _.get(featureObj, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.param_roi`),
      true,
      featureObj,
      componentObj,
      t,
      updateFeature,
      refetchAllFeatures,
      setSelectedAgentParam,
      fcanvasRef,
      updateAllFeaturesState,
      backgroundRoi2,
      updateComponent,
      allFeatures,
      refetchAllComponents,
      updateGroupAgentParams,
    )];
  }

  if (selectedAgentParam === `${solderInspection3D}.${extendedRoi}`) {
    result = [generateSolder3DExtendedRoi(
      featureObj,
      componentObj,
      true,
      t,
      updateFeature,
      refetchAllFeatures,
      allFeatures,
      updateComponent,
      refetchAllComponents,
      setSelectedAgentParam,
      updateAllFeaturesState,
      updateGroupAgentParams,
    )];
  }

  if (selectedAgentParam === `${solderInspection3D}.${profileRoi}`) {
    const editableProfileRect = generateSolder3DProfileRoi(
      featureObj,
      componentObj,
      true,
      t,
      updateFeature,
      refetchAllFeatures,
      allFeatures,
      updateComponent,
      refetchAllComponents,
      setSelectedAgentParam,
      updateAllFeaturesState,
      updateGroupAgentParams,
    );

    // since profile roi's width's limited by extended roi's width so we need to render the extended roi as well
    const extendedRoiRect = generateSolder3DExtendedRoi(
      featureObj,
      componentObj,
      false,
    );

    // viewer will set the first one as active so put profile rect first
    result = [editableProfileRect, extendedRoiRect];
  }

  const leadAgentName = isAOI2DSMT ? leadInspection2D : leadInspection3D;
  if (_.includes(selectedAgentParam, `${leadAgentName}.${leadSegmentationRects}.${extendedRoi}`)) {
    const leadIndex = Number(_.split(selectedAgentParam, '.').pop());
    if (!_.isInteger(leadIndex) || leadIndex < 0) {
      console.warn('Invalid lead index for extended ROI:', selectedAgentParam);
      return [];
    }
    result = generateLead3DSegmentDisplay(
      adaptLeadParamsForDisplay(featureObj),
      mmToPixelRatio,
      fcanvasRef,
      setSelectedAgentParam,
      true,
      selectedAgentParam,
      updateFeature,
      updateAllFeaturesState,
      t,
      allFeatures,
      componentObj,
      updateComponent,
      refetchAllComponents,
      updateGroupAgentParams,
    );
  }

  if (isAOI2DSMT && selectedAgentParam.includes(`${leadInspection2D}.${extTop}`)) {
    result = generateLead2DExtDisplay(
      featureObj,
      mmToPixelRatio,
      fcanvasRef,
      setSelectedAgentParam,
      true,
      selectedAgentParam,
      updateFeature,
      updateAllFeaturesState,
      t,
      allFeatures,
      componentObj,
      updateGroupAgentParams,
      extTop,
    );
  }

  if (isAOI2DSMT && selectedAgentParam.includes(`${leadInspection2D}.${extBot}`)) {
    result = generateLead2DExtDisplay(
      featureObj,
      mmToPixelRatio,
      fcanvasRef,
      setSelectedAgentParam,
      true,
      selectedAgentParam,
      updateFeature,
      updateAllFeaturesState,
      t,
      allFeatures,
      componentObj,
      updateGroupAgentParams,
      extBot,
    );
  }

  // attach is agent param obj
  for (const obj of result) {
    obj.set('isAgentParamObj', true);
  }

  return result;
};

export const getComponentCenterByRoiDtoObj = (obj) => {
  return {
    x: _.get(obj, 'points[0].x', 0) + (_.get(obj, 'points[1].x', 0) - _.get(obj, 'points[0].x', 0) + 1) / 2,
    y: _.get(obj, 'points[0].y', 0) + (_.get(obj, 'points[1].y', 0) - _.get(obj, 'points[0].y', 0) + 1) / 2,
  };
};

const generateMounting2DPolarityRoi = (
  paramRoi,
  interactive,
  featureObj,
  componentObj,
  t,
  updateFeature,
  refetchAllFeatures,
  setSelectedAgentParam,
  fcanvasRef,
  updateAllFeaturesState,
  updateGroupAgentParams,
) => {
  // first find the rotated feature' top left
  // the polarity roi's coord system is rotated by the feature's angle and its origin is the rotated feature's top left
  // const componentCenter = getComponentCenterByRoiDtoObj(_.get(componentObj, 'shape', {}));
  let featureCenter = getComponentCenterByRoiDtoObj(_.get(featureObj, 'roi', {}));
  // featureCenter = rotatePoint(featureCenter, _.get(componentObj, 'shape.angle', 0), componentCenter);
  let featureTopLeft = {
    x: featureCenter.x - (_.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1) / 2,
    y: featureCenter.y - (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1) / 2,
  };
  featureTopLeft = rotatePoint(featureTopLeft, _.get(featureObj, 'roi.angle', 0), featureCenter);
  // polarity roi is based on the feature's top left
  let polarityRoiCenter = getComponentCenterByRoiDtoObj(paramRoi);
  // convert to global coord
  polarityRoiCenter = {
    x: featureTopLeft.x + polarityRoiCenter.x,
    y: featureTopLeft.y + polarityRoiCenter.y,
  };
  // now rotate around the feature's center by the feature's angle
  // we assume the polarity roi angle=feature angle
  polarityRoiCenter = rotatePoint(polarityRoiCenter, _.get(featureObj, 'roi.angle', 0), featureTopLeft);
  // consturct the rect and self rotate
  const rect = new fabric.Rect({
    left: polarityRoiCenter.x - (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1) / 2 - newRectStrokeWidth,
    top: polarityRoiCenter.y - (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1) / 2 - newRectStrokeWidth,
    width: (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1 + newRectStrokeWidth),
    height: (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1 + newRectStrokeWidth),
    fill: 'transparent',
    stroke: getColorByStr(`${mountingInspection2D}.${polarityRoi}`),
    strokeWidth: newRectStrokeWidth,
    strokeUniform: true,
  });
  rect.rotate(_.get(featureObj, 'roi.angle', 0));

  rect.set('agentParamLabel', `${mountingInspection2D}.${polarityRoi}`);

  if (interactive) {
    rect.set('evented', true);
    rect.set('selectable', true);
    // allow translation and scaling
    rect.setControlsVisibility({
      mt: true,
      mb: true,
      ml: true,
      mr: true,
      bl: true,
      br: true,
      tl: true,
      tr: true,
      mtr: false,
    });

    rect.on('modified', () => {
      handlePolarityRoiUpdate({
        rect,
        paramRoi,
        featureObj,
        t,
        updateFeature,
        updateAllFeaturesState,
        componentObj,
        updateGroupAgentParams,
      });
    });

    rect.onDeselect = (opt) => {
      if (!rect) return;
      if (_.isUndefined(opt.e)) return;
      setSelectedAgentParam(null);
    };
  } else {
    rect.set('evented', true);
    rect.set('selectable', true);
    rect.setControlsVisibility({
      mt: false,
      mb: false,
      ml: false,
      mr: false,
      bl: false,
      br: false,
      tl: false,
      tr: false,
      mtr: false,
    });
    rect.hoverCursor = 'pointer';
    rect.on('mousedown', (opt) => {
      if (opt.e.button === 0) {
        setSelectedAgentParam(`${mountingInspection2D}.${polarityRoi}`);
      }
    });
    rect.on('mouseover', () => {
      rect.set('stroke', '#FEB617');
      fcanvasRef.current.renderAll();
    });
    rect.on('mouseout', () => {
      rect.set('stroke', `${getColorByStr(`${mountingInspection2D}.${polarityRoi}`)}`);
      fcanvasRef.current.renderAll();
    });
  }

  return rect;
};

const generateMounting2DMaskRoi = (
  paramRoi,
  interactive,
  featureObj,
  componentObj,
  t,
  updateFeature,
  refetchAllFeatures,
  setSelectedAgentParam,
  fcanvasRef,
  updateAllFeaturesState,
  updateGroupAgentParams,
) => {
  // basically identical to the mounting 2d polarity roi
  // first find the rotated feature' top left
  // the mask roi's coord system is rotated by the feature's angle and its origin is the rotated feature's top left
  // const componentCenter = getComponentCenterByRoiDtoObj(_.get(componentObj, 'shape', {}));
  let featureCenter = getComponentCenterByRoiDtoObj(_.get(featureObj, 'roi', {}));
  // featureCenter = rotatePoint(featureCenter, _.get(componentObj, 'shape.angle', 0), componentCenter);
  let featureTopLeft = {
    x: featureCenter.x - (_.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1) / 2,
    y: featureCenter.y - (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1) / 2,
  };
  featureTopLeft = rotatePoint(featureTopLeft, _.get(featureObj, 'roi.angle', 0), featureCenter);

  // polarity roi is based on the feature's top left
  let maskRoiCenter = getComponentCenterByRoiDtoObj(paramRoi);
  // convert to global coord
  maskRoiCenter = {
    x: featureTopLeft.x + maskRoiCenter.x,
    y: featureTopLeft.y + maskRoiCenter.y,
  };
  // now rotate around the feature's center by the feature's angle
  // we assume the polarity roi angle=feature angle
  maskRoiCenter = rotatePoint(maskRoiCenter, _.get(featureObj, 'roi.angle', 0), featureTopLeft);

  // consturct the rect and self rotate
  const rect = new fabric.Rect({
    left: maskRoiCenter.x - (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1) / 2 - newRectStrokeWidth,
    top: maskRoiCenter.y - (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1) / 2 - newRectStrokeWidth,
    width: (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1 + newRectStrokeWidth),
    height: (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1 + newRectStrokeWidth),
    fill: 'transparent',
    stroke: getColorByStr(`${mountingInspection2D}.${maskRoi}`),
    strokeWidth: newRectStrokeWidth,
    strokeUniform: true,
  });
  rect.rotate(_.get(featureObj, 'roi.angle', 0));

  rect.set('agentParamLabel', `${mountingInspection2D}.${maskRoi}`);

  if (interactive) {
    rect.set('evented', true);
    rect.set('selectable', true);
    // allow translation and scaling
    rect.setControlsVisibility({
      mt: true,
      mb: true,
      ml: true,
      mr: true,
      bl: true,
      br: true,
      tl: true,
      tr: true,
      mtr: false,
    });

    rect.on('modified', () => {
      // get the new current polarity roi center
      // rotate around the current feature top left by -feature angle
      // then get the pmin pmax based on this center and submit
      let payload;
      let newInnerDimension;
      const targetCenter = rotatePoint(rect.getCenterPoint(), -_.get(featureObj, 'roi.angle', 0), featureTopLeft);

      if (rect.scaleX !== 1 || rect.scaleY !== 1) {
        // scaling
        newInnerDimension = {
          width: (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1 + newRectStrokeWidth) * rect.scaleX - newRectStrokeWidth,
          height: (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1 + newRectStrokeWidth) * rect.scaleY - newRectStrokeWidth,
        };
      } else {
        // translate
        newInnerDimension = {
          width: (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1),
          height: (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1),
        };
      }

      // check if it's out of feature's roi
      // we need to rotate the new rect around the feature center by the -feature angle
      // and get the pmin pmax of the feature rect when it's 0 deg
      const tmpMaskCenter = rotatePoint(rect.getCenterPoint(), -_.get(featureObj, 'roi.angle', 0), featureCenter);
      const tmpMaskPMin = {
        x: tmpMaskCenter.x - newInnerDimension.width / 2,
        y: tmpMaskCenter.y - newInnerDimension.height / 2,
      };
      const tmpMaskPMax = {
        x: tmpMaskCenter.x + newInnerDimension.width / 2 - 1,
        y: tmpMaskCenter.y + newInnerDimension.height / 2 - 1,
      };
      const tmpFeaturePMin = {
        x: featureCenter.x - (_.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1) / 2,
        y: featureCenter.y - (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1) / 2,
      };
      const tmpFeaturePMax = {
        x: featureCenter.x + (_.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1) / 2,
        y: featureCenter.y + (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1) / 2,
      };

      if (tmpMaskPMin.x < tmpFeaturePMin.x || tmpMaskPMax.x > tmpFeaturePMax.x ||
        tmpMaskPMin.y < tmpFeaturePMin.y || tmpMaskPMax.y > tmpFeaturePMax.y) {
        aoiAlert(t('notification.error.thisRoiHasToBePleacedWithinTheFeatureRoi'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      const centerInFeatureCoord = {
        x: targetCenter.x - featureTopLeft.x,
        y: targetCenter.y - featureTopLeft.y,
      };

      payload = {
        ...featureObj,
        line_item_params: {
          ...featureObj.line_item_params,
          [mountingInspection2D]: {
            ...featureObj.line_item_params[mountingInspection2D],
            params: {
              ...featureObj.line_item_params[mountingInspection2D].params,
              [maskRoi]: {
                ...featureObj.line_item_params[mountingInspection2D].params[maskRoi],
                param_roi: {
                  ...featureObj.line_item_params[mountingInspection2D].params[maskRoi].param_roi,
                  points: [
                    {
                      x: _.round(centerInFeatureCoord.x - newInnerDimension.width / 2, 0),
                      y: _.round(centerInFeatureCoord.y - newInnerDimension.height / 2, 0),
                    },
                    {
                      x: _.round(centerInFeatureCoord.x + newInnerDimension.width / 2 - 1, 0),
                      y: _.round(centerInFeatureCoord.y + newInnerDimension.height / 2 - 1, 0),
                    }
                  ],
                  center: null,
                  type: 'obb',
                  angle: _.get(featureObj, 'roi.angle', 0),
                },
              },
            },
          },
        }
      };

      const submit = async (payload, componentObj) => {
        if (!_.isInteger(payload.group_id)) {
          const res = await updateFeature({ body: payload, params: { allComponents: _.isInteger(payload.group_id) } });

          if (res.error) {
            aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
            console.error(res.error.message);
            return;
          }

          // await updateAllFeaturesState([featureObj.feature_id], 'update', [payload]);
          if (!_.isEmpty(res.data)) {
            await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
          }
          return;
        }

        const res = await updateGroupAgentParams({
          line_item_params: removeProfileHeightFromPayload(payload).line_item_params,
          product_id: componentObj.definition_product_id,
          step: 0,
          feature_type: payload.feature_type,
          component_id: payload.group_id,
        });

        if (res.error) {
          aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
          console.error(res.error.message);
          return;
        }

        // TODO: wait for backend's update to provide all related feature dto
        if (!_.isEmpty(res.data)) {
          await updateAllFeaturesState(res.data.feature_ids, 'updateGroupParam', res.data.line_item_params);
        }
      };

      submit(payload, componentObj);
    });

    rect.onDeselect = (opt) => {
      if (!rect) return;
      if (_.isUndefined(opt.e)) return;
      setSelectedAgentParam(null);
    };
  } else {
    rect.set('evented', true);
    rect.set('selectable', true);
    rect.setControlsVisibility({
      mt: false,
      mb: false,
      ml: false,
      mr: false,
      bl: false,
      br: false,
      tl: false,
      tr: false,
      mtr: false,
    });

    rect.hoverCursor = 'pointer';
    rect.on('mousedown', (opt) => {
      if (opt.e.button === 0) {
        setSelectedAgentParam(`${mountingInspection2D}.${maskRoi}`);
      }
    });
    rect.on('mouseover', () => {
      rect.set('stroke', '#FEB617');
      fcanvasRef.current.renderAll();
    });
    rect.on('mouseout', () => {
      rect.set('stroke', `${getColorByStr(`${mountingInspection2D}.${maskRoi}`)}`);
      fcanvasRef.current.renderAll();
    });
  }

  return rect;
};

const generateMounting3DBackgroundRoi = (
  paramRoi,
  interactive,
  featureObj,
  componentObj,
  t,
  updateFeature,
  refetchAllFeatures,
  setSelectedAgentParam,
  fcanvasRef,
  updateAllFeaturesState,
  agentParamName,
  updateComponent,
  features,
  refetchAllComponents,
  updateGroupAgentParams,
) => {
  // basically identical to the mounting 2d polarity roi
  // first find the rotated feature' top left
  // the mask roi's coord system is rotated by the feature's angle and its origin is the rotated feature's top left
  // const componentCenter = getComponentCenterByRoiDtoObj(_.get(componentObj, 'shape', {}));
  let featureCenter = getComponentCenterByRoiDtoObj(_.get(featureObj, 'roi', {}));
  // featureCenter = rotatePoint(featureCenter, _.get(componentObj, 'shape.angle', 0), componentCenter);
  let featureTopLeft = {
    x: featureCenter.x - (_.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1) / 2,
    y: featureCenter.y - (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1) / 2,
  };
  featureTopLeft = rotatePoint(featureTopLeft, _.get(featureObj, 'roi.angle', 0), featureCenter);

  // polarity roi is based on the feature's top left
  let maskRoiCenter = getComponentCenterByRoiDtoObj(paramRoi);
  // convert to global coord
  maskRoiCenter = {
    x: featureTopLeft.x + maskRoiCenter.x,
    y: featureTopLeft.y + maskRoiCenter.y,
  };
  // now rotate around the feature's center by the feature's angle
  // we assume the polarity roi angle=feature angle
  maskRoiCenter = rotatePoint(maskRoiCenter, _.get(featureObj, 'roi.angle', 0), featureTopLeft);

  // consturct the rect and self rotate
  const rect = new fabric.Rect({
    left: maskRoiCenter.x - (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1) / 2 - newRectStrokeWidth,
    top: maskRoiCenter.y - (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1) / 2 - newRectStrokeWidth,
    width: (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1 + newRectStrokeWidth),
    height: (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1 + newRectStrokeWidth),
    fill: 'transparent',
    stroke: getColorByStr(`${mountingInspection3D}.${agentParamName}`),
    strokeWidth: newRectStrokeWidth,
    strokeUniform: true,
  });
  rect.rotate(_.get(featureObj, 'roi.angle', 0));

  rect.set('agentParamLabel', `${mountingInspection3D}.${agentParamName}`);

  if (interactive) {
    rect.set('evented', true);
    rect.set('selectable', true);
    // allow translation and scaling
    rect.setControlsVisibility({
      mt: true,
      mb: true,
      ml: true,
      mr: true,
      bl: true,
      br: true,
      tl: true,
      tr: true,
      mtr: false,
    });

    rect.on('modified', () => {
      // get the new current polarity roi center
      // rotate around the current feature top left by -feature angle
      // then get the pmin pmax based on this center and submit
      let payload;
      let newInnerDimension;
      const targetCenter = rotatePoint(rect.getCenterPoint(), -_.get(featureObj, 'roi.angle', 0), featureTopLeft);

      if (rect.scaleX !== 1 || rect.scaleY !== 1) {
        // scaling
        newInnerDimension = {
          width: (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1 + newRectStrokeWidth) * rect.scaleX - newRectStrokeWidth,
          height: (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1 + newRectStrokeWidth) * rect.scaleY - newRectStrokeWidth,
        };
      } else {
        // translate
        newInnerDimension = {
          width: (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1),
          height: (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1),
        };
      }

      // check if it's out of feature's roi
      // we need to rotate the new rect around the feature center by the -feature angle
      // and get the pmin pmax of the feature rect when it's 0 deg
      // const tmpMaskCenter = rotatePoint(rect.getCenterPoint(), -_.get(featureObj, 'roi.angle', 0), featureCenter);
      // const tmpMaskPMin = {
      //   x: tmpMaskCenter.x - newInnerDimension.width / 2,
      //   y: tmpMaskCenter.y - newInnerDimension.height / 2,
      // };
      // const tmpMaskPMax = {
      //   x: tmpMaskCenter.x + newInnerDimension.width / 2 - 1,
      //   y: tmpMaskCenter.y + newInnerDimension.height / 2 - 1,
      // };
      // const tmpFeaturePMin = {
      //   x: featureCenter.x - (_.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1) / 2,
      //   y: featureCenter.y - (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1) / 2,
      // };
      // const tmpFeaturePMax = {
      //   x: featureCenter.x + (_.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1) / 2,
      //   y: featureCenter.y + (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1) / 2,
      // };

      // if (tmpMaskPMin.x < tmpFeaturePMin.x || tmpMaskPMax.x > tmpFeaturePMax.x ||
      //   tmpMaskPMin.y < tmpFeaturePMin.y || tmpMaskPMax.y > tmpFeaturePMax.y) {
      //   aoiAlert(t('notification.error.thisRoiHasToBePleacedWithinTheFeatureRoi'), ALERT_TYPES.COMMON_ERROR);
      //   return;
      // }

      const centerInFeatureCoord = {
        x: targetCenter.x - featureTopLeft.x,
        y: targetCenter.y - featureTopLeft.y,
      };

      payload = {
        ...featureObj,
        line_item_params: {
          ...featureObj.line_item_params,
          [mountingInspection3D]: {
            ...featureObj.line_item_params[mountingInspection3D],
            params: {
              ...featureObj.line_item_params[mountingInspection3D].params,
              [agentParamName]: {
                ...featureObj.line_item_params[mountingInspection3D].params[agentParamName],
                param_roi: {
                  ...featureObj.line_item_params[mountingInspection3D].params[agentParamName].param_roi,
                  points: [
                    {
                      x: _.round(centerInFeatureCoord.x - newInnerDimension.width / 2, 0),
                      y: _.round(centerInFeatureCoord.y - newInnerDimension.height / 2, 0),
                    },
                    {
                      x: _.round(centerInFeatureCoord.x + newInnerDimension.width / 2 - 1, 0),
                      y: _.round(centerInFeatureCoord.y + newInnerDimension.height / 2 - 1, 0),
                    }
                  ],
                  center: null,
                  type: 'obb',
                  // angle: _.get(featureObj, 'roi.angle', 0),
                  angle: 0,
                },
              },
            },
          },
        }
      };

      let cPayload;

      if (componentObj) {
        let relatedFeatures = _.filter(features, f => f.group_id === componentObj.region_group_id);
        relatedFeatures = _.map(relatedFeatures, f => {
          if (f.feature_id === featureObj.feature_id) {
            return payload;
          }
          if (f.feature_type === featureObj.feature_type) {
            return {
              ...f,
              line_item_params: payload.line_item_params,
            };
          }
          return f;
        });

        const newComponentInfo = getComponentRectInfoByFeatures(relatedFeatures, componentObj);

        // component's center might have changed so need to find the new component center rotated around the prev center
        const newComponentCenter = rotatePoint(newComponentInfo.center, _.get(componentObj, 'shape.angle', 0), getComponentCenterByRoiDtoObj(_.get(componentObj, 'shape', {})));

        const newComponentShape = {
          center: null,
          angle: _.get(componentObj, 'shape.angle', 0),
          points: [
            {
              x: _.round(newComponentCenter.x - (newComponentInfo.pMax.x - newComponentInfo.pMin.x + 1) / 2, 0),
              y: _.round(newComponentCenter.y - (newComponentInfo.pMax.y - newComponentInfo.pMin.y + 1) / 2, 0),
            },
            {
              x: _.round(newComponentCenter.x + (newComponentInfo.pMax.x - newComponentInfo.pMin.x + 1) / 2 - 1, 0),
              y: _.round(newComponentCenter.y + (newComponentInfo.pMax.y - newComponentInfo.pMin.y + 1) / 2 - 1, 0),
            }
          ],
          type: 'obb',
        };

        cPayload = {
          ...componentObj,
          shape: newComponentShape,
          // all: true,
        };

        delete cPayload['color_map_uri'];
        delete cPayload['depth_map_uri'];
        delete cPayload['created_at'];
        delete cPayload['modified_at'];
        delete cPayload['can_group_by_package_no'];
        delete cPayload['can_group_by_part_no'];
        delete cPayload['array_index'];
        delete cPayload['cloned'];
        delete cPayload['designator'];
        delete cPayload['variation_for'];
      }
      

      const submit = async (payload, cPayload) => {
        if (!cPayload) {
          const res = await updateFeature({ body: payload, params: { allComponents: _.isInteger(payload.group_id) } });

          if (res.error) {
            aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
            console.error(res.error.message);
            return;
          }

          // await updateAllFeaturesState([featureObj.feature_id], 'update', [payload]);
          if (!_.isEmpty(res.data)) {
            await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
          }
        } else {
          const res = await updateGroupAgentParams({
            line_item_params: removeProfileHeightFromPayload(payload).line_item_params,
            product_id: payload.product_id,
            step: 0,
            feature_type: payload.feature_type,
            component_id: payload.group_id,
          });

          if (res.error) {
            aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
            console.error(res.error.message);
            return;
          }

          const res2 = await updateComponent({
            body: cPayload,
            params: { allComponents: true },
          });
          if (res2.error) {
            aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
            console.error(res2.error.message);
            return;
          }

          await refetchAllComponents();

          if (!_.isEmpty(res.data)) {
            await updateAllFeaturesState(res.data.feature_ids, 'updateGroupParam', res.data.line_item_params);
          }
        }
      };

      submit(payload, cPayload);
    });

    rect.onDeselect = (opt) => {
      if (!rect) return;
      if (_.isUndefined(opt.e)) return;
      setSelectedAgentParam(null);
    };
  } else {
    rect.set('evented', true);
    rect.set('selectable', true);
    rect.setControlsVisibility({
      mt: false,
      mb: false,
      ml: false,
      mr: false,
      bl: false,
      br: false,
      tl: false,
      tr: false,
      mtr: false,
    });

    rect.hoverCursor = 'pointer';
    rect.on('mousedown', (opt) => {
      if (opt.e.button === 0) {
        setSelectedAgentParam(`${mountingInspection3D}.${agentParamName}`);
      }
    });
    rect.on('mouseover', () => {
      rect.set('stroke', '#FEB617');
      fcanvasRef.current.renderAll();
    });
    rect.on('mouseout', () => {
      rect.set('stroke', `${getColorByStr(`${mountingInspection3D}.${agentParamName}`)}`);
      fcanvasRef.current.renderAll();
    });
  }

  return rect;
};

const isMounting3DExtendedRoiEnabled = (featureObj) => {
  if (!_.get(featureObj, `line_item_params.${mountingInspection3D}.enabled`, false)) return false;
  for (const agentParamName of mounting3DExtendedRoiAgentParamNames) {
    if (!_.get(featureObj, `line_item_params.${mountingInspection3D}.params.${agentParamName}.active`, false)) return false;
  }
  return true;
};

export const isMounting2DPolarityRoiEnabled = (featureObj) => {
  return !_.isEmpty(_.get(featureObj, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi`)) &&
  !_.isEmpty(_.get(featureObj, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi.points`, [])) &&
  _.get(featureObj, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.active`, false) &&
  _.get(featureObj, `line_item_params.${mountingInspection2D}.enabled`, false);
};

export const isMounting2DMaskRoiEnabled = (featureObj) => {
  return !_.isEmpty(_.get(featureObj, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi`)) &&
  !_.isEmpty(_.get(featureObj, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi.points`, [])) &&
  _.get(featureObj, `line_item_params.${mountingInspection2D}.params.${maskRoi}.active`, false) &&
  _.get(featureObj, `line_item_params.${mountingInspection2D}.enabled`, false);
};

export const isMounting3DBackgroundRoi1Enabled = (featureObj) => {
  return !_.isEmpty(_.get(featureObj, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi`)) &&
  _.get(featureObj, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.active`, false) &&
  _.get(featureObj, `line_item_params.${mountingInspection3D}.enabled`, false);
};

export const isMounting3DBackgroundRoi2Enabled = (featureObj) => {
  return !_.isEmpty(_.get(featureObj, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.param_roi`)) &&
  _.get(featureObj, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.active`, false) &&
  _.get(featureObj, `line_item_params.${mountingInspection3D}.enabled`, false);
};

const isLead3DEnabled = (featureObj) => {
  return _.get(featureObj, `line_item_params.${leadInspection3D}.enabled`, false) && _.get(featureObj, `line_item_params.${leadInspection2DBase}.enabled`, false);
};

const isLead2DEnabled = (featureObj) => {
  return _.get(featureObj, `line_item_params.${leadInspection2D}.enabled`, false) && _.get(featureObj, `line_item_params.${leadInspection2DBase}.enabled`, false);
};

const adaptLeadParamsForDisplay = (featureObj) => {
  if (!isAOI2DSMT) return featureObj;
  const lead2dParams = _.get(featureObj, `line_item_params.${leadInspection2D}`, null);
  if (!lead2dParams) return featureObj;
  const newObj = _.cloneDeep(featureObj);
  _.set(newObj, `line_item_params.${leadInspection3D}`, {
    ...lead2dParams,
    params: {
      ...lead2dParams.params,
      [lead3DExtTop]: {
        param_int: { value: 0, max: 0, min: 0 },
      },
    },
  });
  return newObj;
};

const isSolder3DEnabled = (featureObj) => {
  return _.get(featureObj, `line_item_params.${solderInspection3D}.enabled`, false);
};

const isOCREnabled = (featureObj) => {
  return _.get(featureObj, `line_item_params.${textVerification}.enabled`, false);
};

export const getComponentRectInfoByFeatures = (features, component) => {
  // NOTE: all calculations are based on the component roi's 0 deg case
  let componentPMinX = Infinity;
  let componentPMinY = Infinity;
  let componentPMaxX = -Infinity;
  let componentPMaxY = -Infinity;

  // consider all features' roi, and all agent params' roi
  for (const f of features) {
    const featureIndividualAng = _.get(f, 'roi.angle', 0) - _.get(component, 'shape.angle', 0);
    let featureCenter = getComponentCenterByRoiDtoObj(_.get(f, 'roi', {}));
    // but we need the feature center at component's 0 deg
    featureCenter = rotatePoint(featureCenter, -_.get(component, 'shape.angle', 0), getComponentCenterByRoiDtoObj(_.get(component, 'shape', {})));
    const featurePMinPMax = {
      pMin: {
        x: featureCenter.x - (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2,
        y: featureCenter.y - (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2,  
      },
      pMax: {
        x: featureCenter.x + (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2,
        y: featureCenter.y + (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2,
      },
    };

    if (isMounting3DExtendedRoiEnabled(f)) {
      const extendedRoiPMinPMax = {
        pMin: {
          x: featurePMinPMax.pMin.x - _.get(f, `line_item_params.${mountingInspection3D}.params.ext_left.param_int.value`, 0),
          y: featurePMinPMax.pMin.y - _.get(f, `line_item_params.${mountingInspection3D}.params.ext_top.param_int.value`, 0),
        },
        pMax: {
          x: featurePMinPMax.pMax.x + _.get(f, `line_item_params.${mountingInspection3D}.params.ext_right.param_int.value`, 0),
          y: featurePMinPMax.pMax.y + _.get(f, `line_item_params.${mountingInspection3D}.params.ext_bottom.param_int.value`, 0),
        }
      };
      let extendedRoiCenter = {
        x: _.get(extendedRoiPMinPMax, 'pMin.x', 0) + (_.get(extendedRoiPMinPMax, 'pMax.x', 0) - _.get(extendedRoiPMinPMax, 'pMin.x', 0)) / 2,
        y: _.get(extendedRoiPMinPMax, 'pMin.y', 0) + (_.get(extendedRoiPMinPMax, 'pMax.y', 0) - _.get(extendedRoiPMinPMax, 'pMin.y', 0)) / 2,
      };

      const rotatedExtRoiCenter = rotatePoint(extendedRoiCenter, featureIndividualAng, featureCenter);
      const rotatedExtRoiPMinPMax = {
        pMin: {
          x: rotatedExtRoiCenter.x - (extendedRoiPMinPMax.pMax.x - extendedRoiPMinPMax.pMin.x) / 2,
          y: rotatedExtRoiCenter.y - (extendedRoiPMinPMax.pMax.y - extendedRoiPMinPMax.pMin.y) / 2,
        },
        pMax: {
          x: rotatedExtRoiCenter.x + (extendedRoiPMinPMax.pMax.x - extendedRoiPMinPMax.pMin.x) / 2,
          y: rotatedExtRoiCenter.y + (extendedRoiPMinPMax.pMax.y - extendedRoiPMinPMax.pMin.y) / 2,
        }
      };

      // apply rotation
      const extendedRotatedRoiPMinPMax = getRotatedRectBoundingBox(rotatedExtRoiPMinPMax.pMin, rotatedExtRoiPMinPMax.pMax, featureIndividualAng);

      componentPMinX = Math.min(componentPMinX, extendedRotatedRoiPMinPMax.pMin.x - componentRoiPadding.left);
      componentPMinY = Math.min(componentPMinY, extendedRotatedRoiPMinPMax.pMin.y - componentRoiPadding.top);
      componentPMaxX = Math.max(componentPMaxX, extendedRotatedRoiPMinPMax.pMax.x + componentRoiPadding.right);
      componentPMaxY = Math.max(componentPMaxY, extendedRotatedRoiPMinPMax.pMax.y + componentRoiPadding.bottom);
    }

    if (isMounting3DBackgroundRoi1Enabled(f)) {
      let featureTopLeft = {
        x: featureCenter.x - (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2,
        y: featureCenter.y - (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2,
      };
      featureTopLeft = rotatePoint(featureTopLeft, featureIndividualAng, featureCenter);
      // background roi's coord system is the same as the feature's
      let backgroundRoiCenter = getComponentCenterByRoiDtoObj(_.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi`, {}));
      // convert to global coord
      backgroundRoiCenter = {
        x: backgroundRoiCenter.x + featureTopLeft.x,
        y: backgroundRoiCenter.y + featureTopLeft.y,
      };
      // rotate
      backgroundRoiCenter = rotatePoint(backgroundRoiCenter, featureIndividualAng, featureTopLeft);
      // get the pmin pmax
      const backgroundRoiPMinPMax = {
        pMin: {
          x: backgroundRoiCenter.x - (_.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi.points[1].x`, 0) - _.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi.points[0].x`, 0) + 1) / 2,
          y: backgroundRoiCenter.y - (_.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi.points[1].y`, 0) - _.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi.points[0].y`, 0) + 1) / 2,
        },
        pMax: {
          x: backgroundRoiCenter.x + (_.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi.points[1].x`, 0) - _.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi.points[0].x`, 0) + 1) / 2,
          y: backgroundRoiCenter.y + (_.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi.points[1].y`, 0) - _.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi1}.param_roi.points[0].y`, 0) + 1) / 2,
        }
      };
      // apply rotation
      const rotatedBackgroundRoiPMinPMax = getRotatedRectBoundingBox(backgroundRoiPMinPMax.pMin, backgroundRoiPMinPMax.pMax, featureIndividualAng);
      componentPMinX = Math.min(componentPMinX, rotatedBackgroundRoiPMinPMax.pMin.x - componentRoiPadding.left);
      componentPMinY = Math.min(componentPMinY, rotatedBackgroundRoiPMinPMax.pMin.y - componentRoiPadding.top);
      componentPMaxX = Math.max(componentPMaxX, rotatedBackgroundRoiPMinPMax.pMax.x + componentRoiPadding.right);
      componentPMaxY = Math.max(componentPMaxY, rotatedBackgroundRoiPMinPMax.pMax.y + componentRoiPadding.bottom);
    }

    if (isMounting3DBackgroundRoi2Enabled(f)) {
      let featureTopLeft = {
        x: featureCenter.x - (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2,
        y: featureCenter.y - (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2,
      };
      featureTopLeft = rotatePoint(featureTopLeft, featureIndividualAng, featureCenter);
      // background roi's coord system is the same as the feature's
      let backgroundRoiCenter = getComponentCenterByRoiDtoObj(_.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.param_roi`, {}));
      // convert to global coord
      backgroundRoiCenter = {
        x: backgroundRoiCenter.x + featureTopLeft.x,
        y: backgroundRoiCenter.y + featureTopLeft.y,
      };
      // rotate
      backgroundRoiCenter = rotatePoint(backgroundRoiCenter, featureIndividualAng, featureTopLeft);
      // get the pmin pmax
      const backgroundRoiPMinPMax = {
        pMin: {
          x: backgroundRoiCenter.x - (_.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.param_roi.points[1].x`, 0) - _.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.param_roi.points[0].x`, 0) + 1) / 2,
          y: backgroundRoiCenter.y - (_.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.param_roi.points[1].y`, 0) - _.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.param_roi.points[0].y`, 0) + 1) / 2,
        },
        pMax: {
          x: backgroundRoiCenter.x + (_.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.param_roi.points[1].x`, 0) - _.get(f, `line_item_params.${mountingInspection3D}.params.${
          backgroundRoi2}.param_roi.points[0].x`, 0) + 1) / 2,
          y: backgroundRoiCenter.y + (_.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.param_roi.points[1].y`, 0) - _.get(f, `line_item_params.${mountingInspection3D}.params.${backgroundRoi2}.param_roi.points[0].y`, 0) + 1) / 2,
        }
      };
      // apply rotation
      const rotatedBackgroundRoiPMinPMax = getRotatedRectBoundingBox(backgroundRoiPMinPMax.pMin, backgroundRoiPMinPMax.pMax, featureIndividualAng);
      componentPMinX = Math.min(componentPMinX, rotatedBackgroundRoiPMinPMax.pMin.x - componentRoiPadding.left);
      componentPMinY = Math.min(componentPMinY, rotatedBackgroundRoiPMinPMax.pMin.y - componentRoiPadding.top);
      componentPMaxX = Math.max(componentPMaxX, rotatedBackgroundRoiPMinPMax.pMax.x + componentRoiPadding.right);
      componentPMaxY = Math.max(componentPMaxY, rotatedBackgroundRoiPMinPMax.pMax.y + componentRoiPadding.bottom);
    }

    if (isLead3DEnabled(f)) {
      const leadIndividualAng = _.get(f, 'roi.angle', 0) - _.get(component, 'shape.angle', 0);

      const extendedRoiPMinPMax = {
        pMin: {
          x: featurePMinPMax.pMin.x,
          y: featurePMinPMax.pMin.y - _.get(f, `line_item_params.${leadInspection3D}.params.${lead3DExtTop}.param_int.value`, 0),
        },
        pMax: {
          x: featurePMinPMax.pMax.x,
          y: featurePMinPMax.pMax.y,
        }
      };

      let extendedRoiCenter = {
        x: _.get(extendedRoiPMinPMax, 'pMin.x', 0) + (_.get(extendedRoiPMinPMax, 'pMax.x', 0) - _.get(extendedRoiPMinPMax, 'pMin.x', 0)) / 2,
        y: _.get(extendedRoiPMinPMax, 'pMin.y', 0) + (_.get(extendedRoiPMinPMax, 'pMax.y', 0) - _.get(extendedRoiPMinPMax, 'pMin.y', 0)) / 2,
      };

      // extendedRoiCenter = rotatePoint(extendedRoiCenter, _.get(component, 'shape.angle', 0), getComponentCenterByRoiDtoObj(_.get(component, 'shape', {})));
      extendedRoiCenter = rotatePoint(extendedRoiCenter, leadIndividualAng, featureCenter);
      
      // const rotatedExtRoiCenter = rotatePoint(extendedRoiCenter, leadIndividualAng, getComponentCenterByRoiDtoObj(_.get(f, 'roi', {})));
      const rotatedExtRoiPMinPMax = {
        pMin: {
          x: extendedRoiCenter.x - (extendedRoiPMinPMax.pMax.x - extendedRoiPMinPMax.pMin.x) / 2,
          y: extendedRoiCenter.y - (extendedRoiPMinPMax.pMax.y - extendedRoiPMinPMax.pMin.y) / 2,
        },
        pMax: {
          x: extendedRoiCenter.x + (extendedRoiPMinPMax.pMax.x - extendedRoiPMinPMax.pMin.x) / 2,
          y: extendedRoiCenter.y + (extendedRoiPMinPMax.pMax.y - extendedRoiPMinPMax.pMin.y) / 2,
        }
      };
      
      const leadExtendedRoiPMinPMax = getRotatedRectBoundingBox(rotatedExtRoiPMinPMax.pMin, rotatedExtRoiPMinPMax.pMax, leadIndividualAng);

      componentPMinX = Math.min(componentPMinX, leadExtendedRoiPMinPMax.pMin.x - componentRoiPadding.left);
      componentPMinY = Math.min(componentPMinY, leadExtendedRoiPMinPMax.pMin.y - componentRoiPadding.top);
      componentPMaxX = Math.max(componentPMaxX, leadExtendedRoiPMinPMax.pMax.x + componentRoiPadding.right);
      componentPMaxY = Math.max(componentPMaxY, leadExtendedRoiPMinPMax.pMax.y + componentRoiPadding.bottom);
    }

    if (isSolder3DEnabled(f)) {
      // extended roi
      const extendedRoiPMinPMax = {
        pMin: {
          x: featurePMinPMax.pMin.x - _.get(f, `line_item_params.${solderInspection3D}.params.ext_left.param_int.value`, 0),
          y: featurePMinPMax.pMin.y - _.get(f, `line_item_params.${solderInspection3D}.params.ext_top.param_int.value`, 0),
        },
        pMax: {
          x: featurePMinPMax.pMax.x + _.get(f, `line_item_params.${solderInspection3D}.params.ext_right.param_int.value`, 0),
          y: featurePMinPMax.pMax.y + _.get(f, `line_item_params.${solderInspection3D}.params.ext_bottom.param_int.value`, 0),
        }
      };
      const extendedRoiCenter = {
        x: _.get(extendedRoiPMinPMax, 'pMin.x', 0) + (_.get(extendedRoiPMinPMax, 'pMax.x', 0) - _.get(extendedRoiPMinPMax, 'pMin.x', 0)) / 2,
        y: _.get(extendedRoiPMinPMax, 'pMin.y', 0) + (_.get(extendedRoiPMinPMax, 'pMax.y', 0) - _.get(extendedRoiPMinPMax, 'pMin.y', 0)) / 2,
      };
      const rotatedExtRoiCenter = rotatePoint(extendedRoiCenter, featureIndividualAng, featureCenter);
      const rotatedExtRoiPMinPMax = {
        pMin: {
          x: rotatedExtRoiCenter.x - (extendedRoiPMinPMax.pMax.x - extendedRoiPMinPMax.pMin.x) / 2,
          y: rotatedExtRoiCenter.y - (extendedRoiPMinPMax.pMax.y - extendedRoiPMinPMax.pMin.y) / 2,
        },
        pMax: {
          x: rotatedExtRoiCenter.x + (extendedRoiPMinPMax.pMax.x - extendedRoiPMinPMax.pMin.x) / 2,
          y: rotatedExtRoiCenter.y + (extendedRoiPMinPMax.pMax.y - extendedRoiPMinPMax.pMin.y) / 2,
        }
      };

      // apply rotation
      const extendedRotatedRoiPMinPMax = getRotatedRectBoundingBox(rotatedExtRoiPMinPMax.pMin, rotatedExtRoiPMinPMax.pMax, featureIndividualAng);

      componentPMinX = Math.min(componentPMinX, extendedRotatedRoiPMinPMax.pMin.x - componentRoiPadding.left);
      componentPMinY = Math.min(componentPMinY, extendedRotatedRoiPMinPMax.pMin.y - componentRoiPadding.top);
      componentPMaxX = Math.max(componentPMaxX, extendedRotatedRoiPMinPMax.pMax.x + componentRoiPadding.right);
      componentPMaxY = Math.max(componentPMaxY, extendedRotatedRoiPMinPMax.pMax.y + componentRoiPadding.bottom);

      const profileHeightVal =
        extendedRoiPMinPMax.pMax.y - extendedRoiPMinPMax.pMin.y + 1;

      // profile roi
      const profileRoiPMinPMax = {
        pMin: {
          x: featureCenter.x - _.get(f, `line_item_params.${solderInspection3D}.params.profile_width.param_int.value`, 0) / 2,
          y: featureCenter.y - profileHeightVal / 2,
        },
        pMax: {
          x: featureCenter.x + _.get(f, `line_item_params.${solderInspection3D}.params.profile_width.param_int.value`, 0) / 2,
          y: featureCenter.y + profileHeightVal / 2,
        }
      };

      const profileRotatedRoiPMinPMax = getRotatedRectBoundingBox(
        profileRoiPMinPMax.pMin,
        profileRoiPMinPMax.pMax,
        featureIndividualAng,
      );

      componentPMinX = Math.min(componentPMinX, profileRotatedRoiPMinPMax.pMin.x - componentRoiPadding.left);
      componentPMinY = Math.min(componentPMinY, profileRotatedRoiPMinPMax.pMin.y - componentRoiPadding.top);
      componentPMaxX = Math.max(componentPMaxX, profileRotatedRoiPMinPMax.pMax.x + componentRoiPadding.right);
      componentPMaxY = Math.max(componentPMaxY, profileRotatedRoiPMinPMax.pMax.y + componentRoiPadding.bottom);
    }

    const rotatedFeaturePMinPMax = getRotatedRectBoundingBox(
      featurePMinPMax.pMin,
      featurePMinPMax.pMax,
      featureIndividualAng,
    );

    componentPMinX = Math.min(componentPMinX, rotatedFeaturePMinPMax.pMin.x - componentRoiPadding.left);
    componentPMinY = Math.min(componentPMinY, rotatedFeaturePMinPMax.pMin.y - componentRoiPadding.top);
    componentPMaxX = Math.max(componentPMaxX, rotatedFeaturePMinPMax.pMax.x + componentRoiPadding.right);
    componentPMaxY = Math.max(componentPMaxY, rotatedFeaturePMinPMax.pMax.y + componentRoiPadding.bottom);
  }

  // console.log('componentPMinX, componentPMinY, componentPMaxX, componentPMaxY', componentPMinX, componentPMinY, componentPMaxX, componentPMaxY);

  return {
    pMin: {
      x: _.round(componentPMinX, 0),
      y: _.round(componentPMinY, 0),
    },
    pMax: {
      x: _.round(componentPMaxX, 0),
      y: _.round(componentPMaxY, 0),
    },
    center: {
      x: (componentPMinX + componentPMaxX) / 2,
      y: (componentPMinY + componentPMaxY) / 2,
    },
  }
};

export const updateAllFeaturesByUpdatedExtendedRoi = ({
  features,
  oldComponentCenter,
  newComponentShape,
  t,
  extendedRoiFeatureId,
  lineItemName,
  newExtLeft,
  newExtRight,
  newExtTop,
  newExtBottom,
}) => {
  const {
    angle,
    center: newComponentCenter,
    // points: newComponentPMinPMax,
  } = newComponentShape;

  const newFeatures = _.map(features, f => {
    // rotate old center around the old center by angle
    // then rotate around the new center by -angle
    let fCenter = rotatePoint(getComponentCenterByRoiDtoObj(_.get(f, 'roi', {})), angle, oldComponentCenter);
    fCenter = rotatePoint(fCenter, -angle, newComponentCenter);

    let newFeatureObj = _.cloneDeep(f);
    newFeatureObj = _.set(newFeatureObj, 'roi.points', [
      {
        x: _.round(fCenter.x - (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2, 0),
        y: _.round(fCenter.y - (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2, 0),
      },
      {
        x: _.round(fCenter.x + (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2 - 1, 0),
        y: _.round(fCenter.y + (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2 - 1, 0),
      }
    ]);

    // if (
    //   f.feature_id === extendedRoiFeatureId &&
    //   _.isNumber(newExtLeft) &&
    //   _.isNumber(newExtRight) &&
    //   _.isNumber(newExtTop) &&
    //   _.isNumber(newExtBottom)
    // ) {
    //   // update the extended roi
    //   newFeatureObj = _.set(newFeatureObj, `line_item_params.${lineItemName}.params.ext_left.param_int.value`, newExtLeft);
    //   newFeatureObj = _.set(newFeatureObj, `line_item_params.${lineItemName}.params.ext_top.param_int.value`, newExtTop);
    //   newFeatureObj = _.set(newFeatureObj, `line_item_params.${lineItemName}.params.ext_right.param_int.value`, newExtRight);
    //   newFeatureObj = _.set(newFeatureObj, `line_item_params.${lineItemName}.params.ext_bottom.param_int.value`, newExtBottom);
    // }

    // check if mounting2d polarity roi is enabled
    if (isMounting2DPolarityRoiEnabled(newFeatureObj)) {
      // check if the current feature roi overlaps the polarity roi
      if (!isCurrentFeatureRectOverlapAgentParamRoi(newFeatureObj, {shape: newComponentShape}, mountingInspection2D, polarityRoi)) {
        aoiAlert(t('notification.error.featureRoiShouldOverlapMounting2dPolarityRoi'), ALERT_TYPES.COMMON_ERROR);
        dispatch(setIsContainerLvlLoadingEnabled(false));
        dispatch(setContainerLvlLoadingMsg(''));
        throw new Error('feature roi should overlap mounting2d polarity roi');
      }
      // get the updated polarity roi in updateAllFeaturesByUpdatedExtendedRoi
    }

    // also check mounting2d mask roi
    if (isMounting2DMaskRoiEnabled(newFeatureObj)) {
      if (!isCurrentFeatureRectOverlapAgentParamRoi(newFeatureObj, {shape: newComponentShape}, mountingInspection2D, maskRoi)) {
        aoiAlert(t('notification.error.featureRoiShouldOverlapMounting2dMaskRoi'), ALERT_TYPES.COMMON_ERROR);
        dispatch(setIsContainerLvlLoadingEnabled(false));
        dispatch(setContainerLvlLoadingMsg(''));
        throw new Error('feature roi should overlap mounting2d mask roi');
      }
    }

    // // update agent param related roi dto
    // if (isMounting2DPolarityRoiEnabled(f)) {
    //   // get current polarity roi center
    //   let oldFCenter = getComponentCenterByRoiDtoObj(_.get(f, 'roi', {}));
    //   oldFCenter = rotatePoint(oldFCenter, angle, oldComponentCenter);
    //   let oldFTopLeft = {
    //     x: oldFCenter.x - (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2,
    //     y: oldFCenter.y - (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2,
    //   };
    //   oldFTopLeft = rotatePoint(oldFTopLeft, _.get(f, 'roi.angle'), oldFCenter);
    //   let polarityRoiCenter = getComponentCenterByRoiDtoObj(_.get(f, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi`, {}));
    //   polarityRoiCenter = rotatePoint(polarityRoiCenter, _.get(f, 'roi.angle'), oldFTopLeft);
    //   // get the new f top left and rotate the polarity roi center around the new f top left by -feature angle to get the new polarity roi center need to be updated
    //   let newFCenter = getComponentCenterByRoiDtoObj(_.get(newFeatureObj, 'roi', {}));
    //   newFCenter = rotatePoint(newFCenter, angle, newComponentCenter);
    //   let newFTopLeft = {
    //     x: newFCenter.x - (_.get(newFeatureObj, 'roi.points[1].x', 0) - _.get(newFeatureObj, 'roi.points[0].x', 0) + 1) / 2,
    //     y: newFCenter.y - (_.get(newFeatureObj, 'roi.points[1].y', 0) - _.get(newFeatureObj, 'roi.points[0].y', 0) + 1) / 2,
    //   };
    //   newFTopLeft = rotatePoint(newFTopLeft, _.get(newFeatureObj, 'roi.angle'), newFCenter);
    //   polarityRoiCenter = rotatePoint(polarityRoiCenter, -_.get(newFeatureObj, 'roi.angle'), newFTopLeft);
    //   newFeatureObj = _.set(newFeatureObj, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi.points`, [
    //     {
    //       x: _.round(polarityRoiCenter.x - (_.get(f, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi.points[1].x`, 0) - _.get(f, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi.points[0].x`, 0) + 1) / 2, 0),
    //       y: _.round(polarityRoiCenter.y - (_.get(f, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi.points[1].y`, 0) - _.get(f, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi.points[0].y`, 0) + 1) / 2, 0),
    //     },
    //     {
    //       x: _.round(polarityRoiCenter.x + (_.get(f, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi.points[1].x`, 0) - _.get(f, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi.points[0].x`, 0) + 1) / 2 - 1, 0),
    //       y: _.round(polarityRoiCenter.y + (_.get(f, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi.points[1].y`, 0) - _.get(f, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi.points[0].y`, 0) + 1) / 2 - 1, 0),
    //     }
    //   ]);
    // }

    // if (isMounting2DMaskRoiEnabled(f)) {
    //   // identical to polarity roi
    //   let oldFCenter = getComponentCenterByRoiDtoObj(_.get(f, 'roi', {}));
    //   oldFCenter = rotatePoint(oldFCenter, angle, oldComponentCenter);
    //   let oldFTopLeft = {
    //     x: oldFCenter.x - (_.get(f, 'roi.points[1].x', 0) - _.get(f, 'roi.points[0].x', 0) + 1) / 2,
    //     y: oldFCenter.y - (_.get(f, 'roi.points[1].y', 0) - _.get(f, 'roi.points[0].y', 0) + 1) / 2,
    //   };
    //   oldFTopLeft = rotatePoint(oldFTopLeft, _.get(f, 'roi.angle'), oldFCenter);
    //   let maskRoiCenter = getComponentCenterByRoiDtoObj(_.get(f, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi`, {}));
    //   maskRoiCenter = rotatePoint(maskRoiCenter, _.get(f, 'roi.angle'), oldFTopLeft);
    //   // get the new f top left and rotate the polarity roi center around the new f top left by -feature angle to get the new polarity roi center need to be updated
    //   let newFCenter = getComponentCenterByRoiDtoObj(_.get(newFeatureObj, 'roi', {}));
    //   newFCenter = rotatePoint(newFCenter, angle, newComponentCenter);
    //   let newFTopLeft = {
    //     x: newFCenter.x - (_.get(newFeatureObj, 'roi.points[1].x', 0) - _.get(newFeatureObj, 'roi.points[0].x', 0) + 1) / 2,
    //     y: newFCenter.y - (_.get(newFeatureObj, 'roi.points[1].y', 0) - _.get(newFeatureObj, 'roi.points[0].y', 0) + 1) / 2,
    //   };
    //   newFTopLeft = rotatePoint(newFTopLeft, _.get(newFeatureObj, 'roi.angle'), newFCenter);
    //   maskRoiCenter = rotatePoint(maskRoiCenter, -_.get(newFeatureObj, 'roi.angle'), newFTopLeft);
    //   newFeatureObj = _.set(newFeatureObj, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi.points`, [
    //     {
    //       x: _.round(maskRoiCenter.x - (_.get(f, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi.points[1].x`, 0) - _.get(f, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi.points[0].x`, 0) + 1) / 2, 0),
    //       y: _.round(maskRoiCenter.y - (_.get(f, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi.points[1].y`, 0) - _.get(f, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi.points[0].y`, 0) + 1) / 2, 0),
    //     },
    //     {
    //       x: _.round(maskRoiCenter.x + (_.get(f, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi.points[1].x`, 0) - _.get(f, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi.points[0].x`, 0) + 1) / 2 - 1, 0),
    //       y: _.round(maskRoiCenter.y + (_.get(f, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi.points[1].y`, 0) - _.get(f, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi.points[0].y`, 0) + 1) / 2 - 1, 0),
    //     }
    //   ]);
    // }

    return newFeatureObj;
  });

  return newFeatures;
};

export const generalDrawRectMouseDownHandle = (opt, fcanvas, callback) => {
  const pointer = fcanvas.getPointer(opt.e);
  const rect = new fabric.Rect({
    left: pointer.x,
    top: pointer.y,
    width: 0,
    height: 0,
    fill: 'transparent',
    stroke: 'red',
    strokeWidth: newRectStrokeWidth,
    selectable: false,
    strokeUniform: true, // Ensure stroke width remains con\sistent when scaling
    evented: false,
  });

  if (callback) callback(rect);
};

export const generalDrawRectMouseMoveHandle = (opt, fcanvas, rect, drawingInitMousePos) => {
  // NOTE: keep rect's width and height positive when drawing and adjust the top left
  // ow the rect's left and right will be hidden for some reason
  const pointer = fcanvas.getPointer(opt.e);

  if (_.get(drawingInitMousePos, 'x') > pointer.x) {
    rect.set({
      left: pointer.x,
      width: drawingInitMousePos.x - pointer.x,
    })
  } else {
    rect.set({
      width: pointer.x - drawingInitMousePos.x,
      left: drawingInitMousePos.x,
    });
  }

  if (_.get(drawingInitMousePos, 'y') > pointer.y) {
    rect.set({
      top: pointer.y,
      height: drawingInitMousePos.y - pointer.y,
    });
  } else {
    rect.set({
      height: pointer.y - drawingInitMousePos.y,
      top: drawingInitMousePos.y,
    });
  }
};

export const getAngleFromReference = (reference, target) => {
  const dx = target.x - reference.x;
  const dy = reference.y - target.y; // because Y is up

  let angleRad = Math.atan2(dx, dy); // Notice: dx first because Y is 0°
  let angleDeg = angleRad * (180 / Math.PI);

  // Normalize to [0, 360)
  if (angleDeg < 0) angleDeg += 360;

  return angleDeg;
};

export const initMountingDefaultLineItemParams = (
  defaultLineItemParams,
  isAOI2DSMT,
  isAOI3DSMT = false,
) => {
  return {
    ...((isAOI2DSMT || isAOI3DSMT) ? {
      [mountingInspection2D]: {
        ...defaultLineItemParams[mountingInspection2D],
        enabled: true,
      },
    } : {}),
    ...(isAOI2DSMT ? {} : {
      [mountingInspection3D]: {
        ...defaultLineItemParams[mountingInspection3D],
        enabled: true,
      },
    }),
  };
};

export const initLeadDefaultLintItemParams = (
  defaultLineItemParams,
  isAOI2DSMT,
  isAOI3DSMT = false,
) => {
  const params = {};

  if (!isAOI2DSMT) {
    params[leadInspection3D] = {
      ...defaultLineItemParams[leadInspection3D],
      enabled: true,
    };
  }

  if (isAOI2DSMT) {
    params[leadInspection2DBase] = {
      ...defaultLineItemParams[leadInspection2DBase],
      enabled: true,
    };
    params[leadInspection2D] = {
      ...defaultLineItemParams[leadInspection2D],
      enabled: true,
    };
  } else if (isAOI3DSMT) {
    params[leadInspection2DBase] = {
      ...defaultLineItemParams[leadInspection2DBase],
      enabled: true,
    };
  }

  return params;
};

export const initSolderDefaultLineItemParams = (defaultLineItemParams, isAOI2DSMT) => {
  return {
    ...(isAOI2DSMT ? {} : {
      [solderInspection3D]: {
        ...defaultLineItemParams[solderInspection3D],
        enabled: true,
      },
    }),
    ...(!isAOI2DSMT ? {} : {
      [solderInspection2D]: {
        ...defaultLineItemParams[solderInspection2D],
        enabled: true,
      },
    }),
  };
};

export const initTextVerificationDefaultLineItemParams = (defaultLineItemParams) => {
  return {
    [textVerification]: {
      ...defaultLineItemParams[textVerification],
      enabled: true,
    },
  };
};

export const initBarcodeScannerDefaultLineItemParams = (defaultLineItemParams) => {
  return {
    [barcodeScanner]: {
      ...defaultLineItemParams[barcodeScanner],
      enabled: true,
    },
  };
};

export const getMMDistanceInPixel = async (distanceInMM, physicalCoordMap, depthMapUri, t) => {
  const payload = {
    depth_map_uri: depthMapUri,
    u: 0,
    v: 0,
    canned_rois: [{
      shape: {
        type: 'obb',
        points: [
          { x: 0, y: 0 },
          { x: distanceInMM, y: distanceInMM },
        ],
        angle: 0,
      },
      checklist: {},
    }]
  };

  const res = await physicalCoordMap(payload);

  if (res.error) {
    aoiAlert(t('notification.error.physicalCoordMap'), ALERT_TYPES.COMMON_ERROR);
    console.error(res.error.message);
    return;
  }

  return _.get(res, 'data[0].shape.points[1].x', 0) - _.get(res, 'data[0].shape.points[0].x', 0);
};

// display obj only, event handle is not included
const generateExtendedRoi = (
  extLeft,
  extTop,
  extRight,
  extBottom,
  featureObj,
  componentObj,
  lineItemName,
) => {
  // 1. rotate feature center around component center by component angle
  // 2. get ext roi center
  // 3. rotate ext roi center around feature center by feature angle
  // 4. generate rect
  // 5. rotate rect by feature angle
  const featureCenter = getComponentCenterByRoiDtoObj(_.get(featureObj, 'roi', {}));
  // const rotatedFeatureCenter = rotatePoint(featureCenter, _.get(componentObj, 'shape.angle', 0), getComponentCenterByRoiDtoObj(_.get(componentObj, 'shape', {})));

  const featureInnerDimension = {
    width: _.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1,
    height: _.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1,
  };

  const nonRotatedInnerDimension = {
    width: _.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1 + _.get(extLeft, 'value', 0) + _.get(extRight, 'value', 0),
    height: _.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1 + _.get(extTop, 'value', 0) + _.get(extBottom, 'value', 0),
  };

  // ext roi top left + width height /2 to get the center
  let extRoiCenter = {
    x: (featureCenter.x - featureInnerDimension.width/2 - _.get(extLeft, 'value', 0)) + nonRotatedInnerDimension.width / 2,
    y: (featureCenter.y - featureInnerDimension.height/2 - _.get(extTop, 'value', 0)) + nonRotatedInnerDimension.height / 2,
  };

  extRoiCenter = rotatePoint(extRoiCenter, _.get(featureObj, 'roi.angle', 0), featureCenter);

  const rect = new fabric.Rect({
    left: extRoiCenter.x - nonRotatedInnerDimension.width / 2 - newRectStrokeWidth,
    top: extRoiCenter.y - nonRotatedInnerDimension.height / 2 - newRectStrokeWidth,
    width: nonRotatedInnerDimension.width + newRectStrokeWidth,
    height: nonRotatedInnerDimension.height + newRectStrokeWidth,
    fill: 'transparent',
    stroke: getColorByStr(`${lineItemName}.${extendedRoi}`),
    strokeWidth: newRectStrokeWidth,
    strokeUniform: true,
    evented: false,
    selectable: false,
    strokeDashArray: defaultDashedArray,
  });

  rect.set('agentParamLabel', `${lineItemName}.${extendedRoi}`);

  // finally self rotate
  rect.rotate(_.get(featureObj, 'roi.angle', 0));

  rect.set('originalRotatedCenter', rect.getCenterPoint());

  return {
    rect,
    nonRotatedInnerDimension,
  };
};

const attachModifiedEventHandleForExtendedRoi = (
  rect,
  componentObj,
  nonRotatedInnerDimension, // original
  featureObj,
  lineItemName,
  setSelectedAgentParam,
  refetchAllFeatures,
  refetchAllComponents,
  updateFeature,
  updateComponent,
  features,
  t,
  updateAllFeaturesState,
  updateGroupAgentParams,
) => {
  rect.on('modified', () => {
    const oldComponentCenter = getComponentCenterByRoiDtoObj(_.get(componentObj, 'shape', {}));
    let featureCenter = getComponentCenterByRoiDtoObj(_.get(featureObj, 'roi', {}));
    let extRoiCenter = rect.getCenterPoint();
    extRoiCenter = rotatePoint(extRoiCenter, -_.get(featureObj, 'roi.angle', 0), featureCenter);
    const curExtRoiInnerDimension = {
      width: rect.width * rect.get('scaleX') - newRectStrokeWidth,
      height: rect.height * rect.get('scaleY') - newRectStrokeWidth,
    };
    let innerPMinPMax = {
      pMin: {
        x: extRoiCenter.x - curExtRoiInnerDimension.width / 2,
        y: extRoiCenter.y - curExtRoiInnerDimension.height / 2,
      },
      pMax: {
        x: extRoiCenter.x + curExtRoiInnerDimension.width / 2 - 1,
        y: extRoiCenter.y + curExtRoiInnerDimension.height / 2 - 1,
      },
    };
    
    // get new ext top left right bottom
    let extLeft = _.round(_.get(featureObj, 'roi.points[0].x', 0) - innerPMinPMax.pMin.x, 0);
    let extTop = _.round(_.get(featureObj, 'roi.points[0].y', 0) - innerPMinPMax.pMin.y, 0);
    let extRight = _.round(innerPMinPMax.pMax.x - _.get(featureObj, 'roi.points[1].x', 0), 0);
    let extBottom = _.round(innerPMinPMax.pMax.y - _.get(featureObj, 'roi.points[1].y', 0), 0);

    // check if it overlap the feature roi completely
    if (
      extLeft <= -1 ||
      extTop <= -1 ||
      extRight <= -1 ||
      extBottom <= -1
    ) {
      aoiAlert(t('notification.error.thisRoiShouldOverlapTheFeatureRoiCompletely'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    extLeft = _.min([extLeft, _.get(featureObj, `line_item_params.${lineItemName}.params.ext_left.param_int.max`, 0)]);
    extTop = _.min([extTop, _.get(featureObj, `line_item_params.${lineItemName}.params.ext_top.param_int.max`, 0)]);
    extRight = _.min([extRight, _.get(featureObj, `line_item_params.${lineItemName}.params.ext_right.param_int.max`, 0)]);
    extBottom = _.min([extBottom, _.get(featureObj, `line_item_params.${lineItemName}.params.ext_bottom.param_int.max`, 0)]);

    // check if the ext exceed the limit
    // if (
    //   extLeft > _.get(featureObj, `line_item_params.${lineItemName}.params.ext_left.param_int.max`, 0) ||
    //   extTop > _.get(featureObj, `line_item_params.${lineItemName}.params.ext_top.param_int.max`, 0) ||
    //   extRight > _.get(featureObj, `line_item_params.${lineItemName}.params.ext_right.param_int.max`, 0) ||
    //   extBottom > _.get(featureObj, `line_item_params.${lineItemName}.params.ext_bottom.param_int.max`, 0)
    // ) {
    //   aoiAlert(t('notification.error.extendedRoiDimensionExceedLimit'), ALERT_TYPES.COMMON_ERROR);
    //   return;
    // }

    const newFeatures = _.map(
      _.filter(features, f => f.group_id === featureObj.group_id),
      (f) => {
      // 2025/07/05: any param update will sync to all features of the same type in the group
      // for here we need to add component check(for update component shape correctly)
      // let backend find all other feature id in the same group then return it in update group agent params response
      if (f.feature_id === featureObj.feature_id || f.feature_type === featureObj.feature_type) {
        // update the mounting 3d extended roi agent params with new values
        let extendedParams = {
          ext_top: {
            ...f.line_item_params[lineItemName].params.ext_top,
            param_int: {
              ...f.line_item_params[lineItemName].params.ext_top.param_int,
              value: extTop
            },
          },
          
          ext_bottom: {
            ...f.line_item_params[lineItemName].params.ext_bottom,
            param_int: {
              ...f.line_item_params[lineItemName].params.ext_bottom.param_int,
              value: extBottom
            },
          }
        };

        if (lineItemName !== solderInspection3D) {
          extendedParams = {
            ...extendedParams,
            ext_right: {
              ...f.line_item_params[lineItemName].params.ext_right,
              param_int: {
                ...f.line_item_params[lineItemName].params.ext_right.param_int,
                value: extRight
              },
            },
            ext_left: {
              ...f.line_item_params[lineItemName].params.ext_left,
              param_int: {
                ...f.line_item_params[lineItemName].params.ext_left.param_int,
                value: extLeft
              },
            }
          };
        }

        if (lineItemName === solderInspection3D) {
          // we also want to update the profile roi since we need to keep the profile roi's height = extended roi's height
          extendedParams = {
            ...extendedParams,
            // profile_height: {
            //   ...f.line_item_params[lineItemName].params.profile_height,
            //   param_int: {
            //     ...f.line_item_params[lineItemName].params.profile_height.param_int,
            //     value: extTop + extBottom + _.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1,
            //   },
            // },
          }
        }

        return {
          ...f,
          line_item_params: {
            ...f.line_item_params,
            [lineItemName]: {
              ...f.line_item_params[lineItemName],
              params: {
                ...f.line_item_params[lineItemName].params,
                ...extendedParams,
              },
            },
          }
        };
      } return f;
    });

    // if the feature is not grouped into a component then just update feature directly
    if (componentObj) {
      // regenerate the new component center
      const {
        pMin,
        pMax,
        center: newComponentNoRotationCenter
      } = getComponentRectInfoByFeatures(newFeatures, componentObj);

      // component's center might have changed so need to find the new component center rotated around the prev center
      const newComponentCenter = rotatePoint(newComponentNoRotationCenter, _.get(componentObj, 'shape.angle', 0), oldComponentCenter);

      const newComponentShape = {
        center: null,
        angle: _.get(componentObj, 'shape.angle', 0),
        points: [
          {
            x: _.round(newComponentCenter.x - (pMax.x - pMin.x + 1) / 2, 0),
            y: _.round(newComponentCenter.y - (pMax.y - pMin.y + 1) / 2, 0),
          },
          {
            x: _.round(newComponentCenter.x + (pMax.x - pMin.x + 1) / 2 - 1, 0),
            y: _.round(newComponentCenter.y + (pMax.y - pMin.y + 1) / 2 - 1, 0),
          }
        ],
        type: 'obb',
      };

      // update all feature roi and all agent params' roi related to this component
      const submit = async (
        features,
        componentObj,
        oldComponentCenter,
        featureId,
        refetchAllFeatures,
        refetchAllComponents,
        newComponentShape,
        newExtLeft,
        newExtTop,
        newExtRight,
        newExtBottom,
        updateFeature,
        updateComponent,
        lineItemName,
        updateGroupAgentParams,
      ) => {
        const feature = _.find(features, f => f.feature_id === featureId);
        const updateGroupRes = await updateGroupAgentParams({
          line_item_params: removeProfileHeightFromPayload(feature).line_item_params,
          product_id: feature.product_id,
          step: 0,
          feature_type: feature.feature_type,
          component_id: feature.group_id,
        });

        if (updateGroupRes.error) {
          aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
          console.error(updateGroupRes.error.message);
          return;
        }

        const cPayload = {
          ...componentObj,
          shape: newComponentShape,
          // all: true,
        };

        delete cPayload['color_map_uri'];
        delete cPayload['depth_map_uri'];
        delete cPayload['created_at'];
        delete cPayload['modified_at'];
        delete cPayload['can_group_by_package_no'];
        delete cPayload['can_group_by_part_no'];
        delete cPayload['array_index'];
        delete cPayload['cloned'];
        delete cPayload['designator'];
        delete cPayload['variation_for'];

        const res = await updateComponent({
          body: cPayload,
          params: { allComponents: true },
        });

        if (res.error) {
          aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
          console.error(res.error.message);
          return;
        }

        await refetchAllComponents();

        if (!_.isEmpty(updateGroupRes.data)) {
          await updateAllFeaturesState(updateGroupRes.data.feature_ids, 'updateGroupParam', updateGroupRes.data.line_item_params);
        }
      };

      submit(
        newFeatures,
        componentObj,
        oldComponentCenter,
        featureObj.feature_id,
        refetchAllFeatures,
        refetchAllComponents,
        newComponentShape,
        extLeft,
        extTop,
        extRight,
        extBottom,
        updateFeature,
        updateComponent,
        lineItemName,
        updateGroupAgentParams,
      ).then(() => {
        // setSelectedAgentParam(null);
      });
    } else {
      const submit = async (features, featureId, refetchAllFeatures, updateFeature) => {
        const feature = _.find(features, f => f.feature_id === featureId);
        const updateFeatureRes = await updateFeature({ body: feature, params: { allComponents: _.isInteger(feature.group_id) } });

        if (updateFeatureRes.error) {
          aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
          console.error(updateFeatureRes.error.message);
          return;
        }

        // await updateAllFeaturesState([feature.feature_id], 'update', [feature]);
        if (!_.isEmpty(updateFeatureRes.data)) {
          await updateAllFeaturesState(_.map(updateFeatureRes.data, 'feature_id'), 'update', updateFeatureRes.data);
        }
      };

      submit(
        newFeatures,
        featureObj.feature_id,
        refetchAllFeatures,
        updateFeature,
      ).then(() => {
        // setSelectedAgentParam(null);
      });
    }
  });
};

const generateProfileRoi = (
  featureObj,
  lineItemName,
  componentObj,
) => {
  // we want the extended roi's center
  const featureCenter = getComponentCenterByRoiDtoObj(_.get(featureObj, 'roi', {}));
  const extendedRoiPMinPMax = {
    pMin: {
      x: _.get(featureObj, 'roi.points[0].x', 0),
      y: _.get(featureObj, 'roi.points[0].y', 0) - _.get(featureObj, `line_item_params.${lineItemName}.params.ext_top.param_int.value`, 0),
    },
    pMax: {
      x: _.get(featureObj, 'roi.points[1].x', 0),
      y: _.get(featureObj, 'roi.points[1].y', 0) + _.get(featureObj, `line_item_params.${lineItemName}.params.ext_bottom.param_int.value`, 0),
    },
  };
  let extendedRoiCenter = {
    x: (extendedRoiPMinPMax.pMin.x + extendedRoiPMinPMax.pMax.x) / 2,
    y: (extendedRoiPMinPMax.pMin.y + extendedRoiPMinPMax.pMax.y) / 2,
  };

  extendedRoiCenter = rotatePoint(extendedRoiCenter, _.get(featureObj, 'roi.angle', 0), featureCenter);

  const profileWidthVal = _.get(featureObj, `line_item_params.${lineItemName}.params.${profileWidth}.param_int.value`, 0);
  const profileHeightVal =
    extendedRoiPMinPMax.pMax.y - extendedRoiPMinPMax.pMin.y + 1;

  const rect = new fabric.Rect({
    left: extendedRoiCenter.x - newRectStrokeWidth - profileWidthVal / 2,
    top: extendedRoiCenter.y - newRectStrokeWidth - profileHeightVal / 2,
    width: profileWidthVal + newRectStrokeWidth,
    height: profileHeightVal + newRectStrokeWidth,
    fill: 'transparent',
    stroke: getColorByStr(`${lineItemName}.${profileRoi}`),
    strokeUniform: true,
    evented: false,
    selectable: false,
    centeredScaling: true,
    strokeWidth: newRectStrokeWidth,
  });

  rect.set('originalRectTopLeftWithZeroRotation', {
    left: extendedRoiCenter.x - newRectStrokeWidth - profileWidthVal / 2,
    top: extendedRoiCenter.y - newRectStrokeWidth - profileHeightVal / 2,
  });
  rect.set('agentParamLabel', `${lineItemName}.${profileRoi}`);

  if (_.get(featureObj, 'roi.angle', 0) !== 0) {
    rect.rotate(_.get(featureObj, 'roi.angle', 0));
    rect.set('originalRotatedCenter', rect.getCenterPoint());
  }

  return {
    rect,
  };
};

const attachModifiedEventForProfileRoi = (
  rect,
  componentObj,
  featureObj,
  lineItemName,
  updateFeature,
  refetchAllFeatures,
  t,
  updateAllFeaturesState,
  updateGroupAgentParams,
) => {
  // similar to mounting2d's polarity roi
  // for now profile roi can only be centered scaled, no translation is allowed
  rect.on('modified', () => {
    let payload;
    let pMin;
    let pMax;

    if (rect.scaleX === 1 && rect.scaleY === 1) return;

    // scaling
    const newNonRotatedCenter = rotatePoint(rect.getCenterPoint(), -_.get(featureObj, 'roi.angle', 0), getComponentCenterByRoiDtoObj(_.get(featureObj, 'roi', {})));
    const rectInnerDimension = {
      width: (
        _.get(featureObj, `line_item_params.${solderInspection3D}.params.${profileWidth}.param_int.value`)
        + newRectStrokeWidth
      ) * rect.scaleX - newRectStrokeWidth,
      height: (
        (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1
          + _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_top.param_int.value`, 0)
          + _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_bottom.param_int.value`, 0)
        )
        + newRectStrokeWidth
      ) * rect.scaleY - newRectStrokeWidth,
    };
    pMin = {
      x: newNonRotatedCenter.x - rectInnerDimension.width / 2,
      y: newNonRotatedCenter.y - rectInnerDimension.height / 2,
    };
    pMax = {
      x: newNonRotatedCenter.x + rectInnerDimension.width / 2 - 1,
      y: newNonRotatedCenter.y + rectInnerDimension.height / 2 - 1,
    };

    // check if it's out of extended's roi
    const extendedRoiPMinPMax = {
      pMin: {
        x: _.get(featureObj, 'roi.points[0].x', 0) - _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_left.param_int.value`, 0),
        y: _.get(featureObj, 'roi.points[0].y', 0) - _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_top.param_int.value`, 0),
      },
      pMax: {
        x: _.get(featureObj, 'roi.points[1].x', 0) + _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_right.param_int.value`, 0),
        y: _.get(featureObj, 'roi.points[1].y', 0) + _.get(featureObj, `line_item_params.${solderInspection3D}.params.ext_bottom.param_int.value`, 0),
      }
    };

    rectInnerDimension.width = _.min([rectInnerDimension.width, _.round(extendedRoiPMinPMax.pMax.x - extendedRoiPMinPMax.pMin.x, 0)]);
    // rectInnerDimension.height = _.min([rectInnerDimension.height, _.get(featureObj, `line_item_params.${solderInspection3D}.params.${profileHeight}.param_int.max`, 0)]);

    // if (
    //   pMin.x < extendedRoiPMinPMax.pMin.x ||
    //   // pMin.y < extendedRoiPMinPMax.pMin.y ||
    //   pMax.x > extendedRoiPMinPMax.pMax.x
    //   // pMax.y > extendedRoiPMinPMax.pMax.y
    // ) {
    //   console.log('profile roi pmin', pMin);
    //   console.log('profile roi pmax', pMax);
    //   console.log('extended roi pmin', extendedRoiPMinPMax.pMin);
    //   console.log('extended roi pmax', extendedRoiPMinPMax.pMax);
    //   aoiAlert(t('notification.error.theExtendedRoiShouldOverlapProfileRoiCompletely'), ALERT_TYPES.COMMON_ERROR);
    //   return;
    // }

    payload = {
      ...featureObj,
      line_item_params: {
        ...featureObj.line_item_params,
        [lineItemName]: {
          ...featureObj.line_item_params[lineItemName],
          params: {
            ...featureObj.line_item_params[lineItemName].params,
            [profileWidth]: {
              ...featureObj.line_item_params[lineItemName].params[profileWidth],
              param_int: {
                ...featureObj.line_item_params[lineItemName].params[profileWidth].param_int,
                value: _.floor(rectInnerDimension.width, 0),
              },
            },
            // [profileHeight]: {
            //   ...featureObj.line_item_params[lineItemName].params[profileHeight],
            //   param_int: {
            //     ...featureObj.line_item_params[lineItemName].params[profileHeight].param_int,
            //     // value: _.floor(rectInnerDimension.height, 0),
            //     value: _.floor(extendedRoiPMinPMax.pMax.y - extendedRoiPMinPMax.pMin.y, 0),
            //   },
            // },
          },
        },
      }
    };

    const submit = async (payload) => {
      if (!_.isInteger(payload.group_id)) {
        const res = await updateFeature({ body: payload, params: { allComponents: _.isInteger(payload.group_id) } });

        if (res.error) {
          aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
          console.error(res.error.message);
          return;
        }

        // await updateAllFeaturesState([payload.feature_id], 'update', [payload]);
        if (!_.isEmpty(res.data)) {
          await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
        }
      } else {
        const res = await updateGroupAgentParams({
          line_item_params: removeProfileHeightFromPayload(payload).line_item_params,
          product_id: payload.product_id,
          step: 0,
          feature_type: payload.feature_type,
          component_id: payload.group_id,
        });

        if (res.error) {
          aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
          console.error(res.error.message);
          return;
        }

        if (!_.isEmpty(res.data)) {
          await updateAllFeaturesState(res.data.feature_ids, 'updateGroupParam', res.data.line_item_params);
        }
      }
    };

    submit(payload);
  });
};

/**
 * Get the AABB (axis-aligned bounding box) after rotating a rectangle.
 * @param {Object} pMin - { x, y } - the top-left corner of the original rect
 * @param {Object} pMax - { x, y } - the bottom-right corner of the original rect
 * @param {number} angle - rotation angle in radians
 * @param {Object} [origin] - { x, y } - optional rotation origin (defaults to center)
 * @returns {{ pMin: {x, y}, pMax: {x, y} }} bounding box of rotated rect
 */
export const getRotatedRectBoundingBox = (pMin, pMax, angle) => {
  const rect = new fabric.Rect({
    left: pMin.x - newRectStrokeWidth,
    top: pMin.y - newRectStrokeWidth,
    width: pMax.x - pMin.x + newRectStrokeWidth,
    height: pMax.y - pMin.y + newRectStrokeWidth,
    strokeWidth: newRectStrokeWidth,
  });
  rect.rotate(angle);
  const aabb = rect.getBoundingRect();

  return {
    pMin: {
      x: aabb.left - newRectStrokeWidth,
      y: aabb.top - newRectStrokeWidth,
    },
    pMax: {
      x: aabb.left + aabb.width,
      y: aabb.top + aabb.height,
    }
  };
};

export const getCenterByPMinPMax = (pmin, pmax) => {
  return {
    x: pmin.x + (pmax.x - pmin.x) / 2,
    y: pmin.y + (pmax.y - pmin.y) / 2,
  };
};

export const getDimensionByPMinPMax = (pmin, pmax) => {
  return {
    width: pmax.x - pmin.x,
    height: pmax.y - pmin.y,
  }
};

export const getDimensionByPMinPMaxInBackendFormat = (pmin, pmax) => {
  return {
    width: pmax.x - pmin.x + 1,
    height: pmax.y - pmin.y + 1,
  };
};

export const generateFabricArrow = ({angleDeg, center, dimension, color, isFilled=true }) => {
  const { x: centerX, y: centerY } = center;
  const { width: arrowWidth, height: arrowHeight } = dimension;

  // Create line shaft (adjusted calculation)
  const lineLength = arrowWidth - arrowHeight;
  const line = new fabric.Line([
      -lineLength / 2, 0,
      lineLength / 2, 0
  ], {
      stroke: color,
      strokeWidth: 2,
      originX: 'center',
      originY: 'center'
  });

  // Create arrowhead triangle (positioned after line)
  let triangle;
  if (isFilled) {
    triangle = new fabric.Triangle({
        width: arrowWidth/2,
        height: arrowHeight,
        fill: color,
        // originX: 'center',
        // originY: 'center',
        left: centerX - arrowWidth / 4,
        top: centerY - arrowHeight / 2,
        evented: false,
        selectable: false,
    });
  } else {
    triangle = new fabric.Triangle({
        width: arrowWidth/2,
        height: arrowHeight,
        stroke: color,
        strokeWidth: 1,
        fill: 'transparent',
        left: centerX - arrowWidth / 4,
        top: centerY - arrowHeight / 2,
        evented: false,
        selectable: false,
    });
  }
  triangle.rotate(angleDeg);

  return triangle;

  // Create group with proper transformation origin
  // return new fabric.Group([line, triangle], {
  //     left: centerX,
  //     top: centerY,
  //     angle: angleDeg,
  //     originX: 'center',
  //     originY: 'center'
  // });
};

export const isCurrentFeatureRectOverlapAgentParamRoi = (feature, component, lineItemName, agentParamName) => {
  if (!isMounting2DPolarityRoiEnabled(feature)) return false;

  const featurePMinPMaxInFeatureCoord = {
    pMin: {
      x: 0,
      y: 0,
    },
    pMax: {
      x: _.get(feature, 'roi.points[1].x', 0) - _.get(feature, 'roi.points[0].x', 0) + 1,
      y: _.get(feature, 'roi.points[1].y', 0) - _.get(feature, 'roi.points[0].y', 0) + 1,
    }
  };

  const agentParamRoiPMinPMaxInFeatureCoord = {
    pMin: {
      x: _.get(feature, `line_item_params.${lineItemName}.params.${agentParamName}.param_roi.points[0].x`, 0),
      y: _.get(feature, `line_item_params.${lineItemName}.params.${agentParamName}.param_roi.points[0].y`, 0),
    },
    pMax: {
      x: _.get(feature, `line_item_params.${lineItemName}.params.${agentParamName}.param_roi.points[1].x`, 0),
      y: _.get(feature, `line_item_params.${lineItemName}.params.${agentParamName}.param_roi.points[1].y`, 0),
    }
  };

  // console.log('featurePMinPMaxInFeatureCoord', featurePMinPMaxInFeatureCoord);
  // console.log('agentParamRoiPMinPMaxInFeatureCoord', agentParamRoiPMinPMaxInFeatureCoord);

  return featurePMinPMaxInFeatureCoord.pMin.x < agentParamRoiPMinPMaxInFeatureCoord.pMin.x &&
    featurePMinPMaxInFeatureCoord.pMin.y < agentParamRoiPMinPMaxInFeatureCoord.pMin.y &&
    featurePMinPMaxInFeatureCoord.pMax.x > agentParamRoiPMinPMaxInFeatureCoord.pMax.x &&
    featurePMinPMaxInFeatureCoord.pMax.y > agentParamRoiPMinPMaxInFeatureCoord.pMax.y;
};

export const handleSceneCopyComponentRect = async (
  componentObj,
  relatedFeatures,
  addComponent,
  addFeatureRoi,
  t,
  lazyGetAllFeatures,
  curProduct,
) => {
  // add component first
  const cPayload = {
    definition_product_id: _.get(componentObj, 'definition_product_id', 0),
    definition_step: _.get(componentObj, 'definition_step', 0),
    designator: `${_.get(componentObj, 'designator', '')}_copy`,
    package_no: _.get(componentObj, 'package_no', ''),
    part_no: _.get(componentObj, 'part_no', ''),
    center: _.get(componentObj, 'shape.center', {}),
    shape: {
      ..._.get(componentObj, 'shape', {}),
      points: [
        {
          x: _.get(componentObj, 'shape.points[0].x', 0) + sceneCopyRectBottomRightOffset,
          y: _.get(componentObj, 'shape.points[0].y', 0) + sceneCopyRectBottomRightOffset,
        },
        {
          x: _.get(componentObj, 'shape.points[1].x', 0) + sceneCopyRectBottomRightOffset,
          y: _.get(componentObj, 'shape.points[1].y', 0) + sceneCopyRectBottomRightOffset,
        }
      ]
    }
  };

  const res = await addComponent(cPayload);

  if (res.error) {
    aoiAlert(t('notification.error.addComponent'), ALERT_TYPES.COMMON_ERROR);
    console.error(res.error.message);
    throw new Error('Failed to add component');
  }

  if (_.get(res, 'data.cloned', false)) {
    // in this case backend will create features for the new component
    // so we need to get the features from backend
    const res1 = await lazyGetAllFeatures({
      product_id: Number(_.get(curProduct, 'product_id', 0)),
      step: 0,
      variant: _.get(curProduct, 'product_name', ''),
      marker: false,
      component_id: _.get(res, 'data.components[0].region_group_id', 0),
    });

    return {
      cid: _.get(res, 'data.components[0].region_group_id', 0),
      fids: _.map(res1.data, 'feature_id'),
      newFeatureObjs: res1.data,
    };
  }

  // not cloned then we need to add features manually
  // ex. group some ungrouped features into a new group component
  const fids = [];
  const newFeatureObjs = [];

  // add features
  for (const feature of relatedFeatures) {
    const payload = {
      step: _.get(feature, 'step', 0),
      product_id: _.get(feature, 'product_id', 0),
      roi: {
        type: 'obb',
        points: [
          {
            x: _.get(feature, 'roi.points[0].x', 0) + sceneCopyRectBottomRightOffset,
            y: _.get(feature, 'roi.points[0].y', 0) + sceneCopyRectBottomRightOffset,
          },
          {
            x: _.get(feature, 'roi.points[1].x', 0) + sceneCopyRectBottomRightOffset,
            y: _.get(feature, 'roi.points[1].y', 0) + sceneCopyRectBottomRightOffset,
          }
        ],
        angle: _.get(feature, 'roi.angle', 0),
      },
      feature_type: _.get(feature, 'feature_type', ''),
      feature_scope: 'product',
      line_item_params: _.get(feature, 'line_item_params', {}),
      group_id: _.get(res, 'data.components[0].region_group_id', 0),
    }

    const res2 = await addFeatureRoi({
      body: removeProfileHeightFromPayload(payload),
      params: {
        allComponents: false,
      },
    });

    if (res2.error) {
      aoiAlert(t('notification.error.addFeature'), ALERT_TYPES.COMMON_ERROR);
      console.error(res2.error.message);
      throw new Error('Failed to add feature');
    }

    fids.push(_.get(res2.data, '[0].feature_id', 0));
    newFeatureObjs.push(_.get(res2, 'data[0]', {}));
  }

  return {
    cid: _.get(res, 'data.components[0].region_group_id', 0),
    fids,
    newFeatureObjs,
  };
};

export const handleSceneRemoveComponentWithRelatedFeatures = async (
  componentObj,
  relatedFeatures,
  removeComponent,
  removeFeature,
) => {
  const res = await removeComponent({
    definition_product_id: _.get(componentObj, 'definition_product_id', 0),
    definition_step: _.get(componentObj, 'definition_step', 0),
    region_group_id: _.get(componentObj, 'region_group_id', 0),
  });

  if (res.error) {
    aoiAlert(t('notification.error.removeComponent'), ALERT_TYPES.COMMON_ERROR);
    console.error(res.error.message);
    throw new Error('Failed to remove component');
  }

  const fids = [];

  for (const feature of relatedFeatures) {
    const res = await removeFeature(feature);

    if (res.error) {
      aoiAlert(t('notification.error.removeFeature'), ALERT_TYPES.COMMON_ERROR);
      console.error(res.error.message);
      throw new Error('Failed to remove feature');
    }

    fids.push(..._.get(res, 'data.feature_ids', []));
  }

  return { fids };
};

export const handleSceneCopyFeatureIntoComponent = async (
  componentObj,
  feature,
  addFeatureRoi,
  updateComponent,
  allFeatures,
) => {
  const payload = {
    step: _.get(feature, 'step', 0),
    product_id: _.get(feature, 'product_id', 0),
    roi: {
      type: 'obb',
      points: [
        {
          x: _.get(feature, 'roi.points[0].x', 0) + sceneCopyRectBottomRightOffset,
          y: _.get(feature, 'roi.points[0].y', 0) + sceneCopyRectBottomRightOffset,
        },
        {
          x: _.get(feature, 'roi.points[1].x', 0) + sceneCopyRectBottomRightOffset,
          y: _.get(feature, 'roi.points[1].y', 0) + sceneCopyRectBottomRightOffset,
        }
      ],
      angle: _.get(feature, 'roi.angle', 0),
    },
    feature_type: _.get(feature, 'feature_type', ''),
    feature_scope: 'product',
    line_item_params: _.get(feature, 'line_item_params', {}),
    group_id: _.get(componentObj, 'region_group_id', 0),
  }

  const res = await addFeatureRoi({ body: removeProfileHeightFromPayload(payload), params: { allComponents: true } });

  if (res.error) throw new Error('Failed to add feature');

  // const cPayload = {
  //   ...componentObj,
  //   feature_ids: [
  //     ...featureIds,
  //     _.get(res, 'data.feature_id', 0),
  //   ],
  // };

  // delete cPayload['color_map_uri'];
  // delete cPayload['depth_map_uri'];
  // delete cPayload['created_at'];
  // delete cPayload['modified_at'];
  // delete cPayload['can_group_by_package_no'];
  // delete cPayload['can_group_by_part_no'];

  // const res2 = await updateComponent(cPayload);

  // if (res2.error) throw new Error('Failed to update component');

  return {
    fid: _.get(res, 'data.feature_id', 0),
    featureObj: _.map(res.data, f => ({
      ...f,
      group_id: _.get(componentObj, 'region_group_id', 0), // assign the component's region_group_id to the new feature
      line_item_params: payload.line_item_params,
    })),
  }
};

export const handleSceneRemoveFeatureFromComponent = async (
  componentObj,
  feature,
  addComponent,
  deleteComponent,
  allFeatures,
) => {
  const res = await deleteComponent({
    definition_product_id: _.get(componentObj, 'definition_product_id', 0),
    definition_step: _.get(componentObj, 'definition_step', 0),
    region_group_id: _.get(componentObj, 'region_group_id', 0),
  });

  if (res.error) throw new Error('Failed to delete component');

  const featureIds = _.map(_.filter(allFeatures, f => f.group_id === componentObj.region_group_id), f => f.feature_id);

  const features = _.filter(allFeatures, f => f.feature_id !== feature.feature_id && f.group_id === componentObj.region_group_id);

  const newComponentInfo = getComponentRectInfoByFeatures(features, componentObj);

  const cPayload = {
    definition_product_id: _.get(componentObj, 'definition_product_id', 0),
    definition_step: _.get(componentObj, 'definition_step', 0),
    designator: `${_.get(componentObj, 'designator', '')}`,
    package_no: _.get(componentObj, 'package_no', ''),
    part_no: _.get(componentObj, 'part_no', ''),
    center: _.get(componentObj, 'shape.center', {}),
    feature_ids: _.filter(featureIds, fid => fid !== feature.feature_id),
    shape: {
      ..._.get(componentObj, 'shape', {}),
      points: [
        {
          x: _.get(newComponentInfo, 'pMin.x', 0),
          y: _.get(newComponentInfo, 'pMin.y', 0),
        },
        {
          x: _.get(newComponentInfo, 'pMax.x', 0),
          y: _.get(newComponentInfo, 'pMax.y', 0),
        }
      ]
    }
  };

  const res1 = await addComponent(cPayload);

  if (res1.error) throw new Error('Failed to update component');

  return {
    cid: _.get(res1, 'data.region_group_id', 0),
  }
};

export const handleSceneRemoveFeature = async (
  featureObj,
  deleteFeature,
  allFeatures,
  updateComponent,
  allComponents,
  selectedArrayIndex,
) => {
  const componentObj = _.find(allComponents, c => c.region_group_id === featureObj.group_id, c => c.array_index === selectedArrayIndex);
  if (!componentObj) throw new Error('Component not found');

  const fPayload = {
    product_id: featureObj.product_id,
    step: featureObj.step,
    feature_id: featureObj.feature_id,
  };

  if (_.isInteger(featureObj.group_id)) fPayload.component_id = featureObj.group_id;

  const res = await deleteFeature(fPayload);

  if (res.error) throw new Error('Failed to delete feature');

  // also update component rect's size in group level
  const features = _.filter(allFeatures, f => f.group_id === featureObj.group_id && f.feature_id !== featureObj.feature_id && f.array_index === selectedArrayIndex);
  const newComponentInfo = getComponentRectInfoByFeatures(features, componentObj);

  const cPayload = {
    ...componentObj,
    shape: {
      ..._.get(componentObj, 'shape', {}),
      points: [
        {
          x: _.get(newComponentInfo, 'pMin.x', 0),
          y: _.get(newComponentInfo, 'pMin.y', 0),
        },
        {
          x: _.get(newComponentInfo, 'pMax.x', 0),
          y: _.get(newComponentInfo, 'pMax.y', 0),
        }
      ]
    },
    // all: true,
  };

  delete cPayload['color_map_uri'];
  delete cPayload['depth_map_uri'];
  delete cPayload['created_at'];
  delete cPayload['modified_at'];
  delete cPayload['can_group_by_package_no'];
  delete cPayload['can_group_by_part_no'];
  delete cPayload['array_index'];
  delete cPayload['cloned'];
  delete cPayload['designator'];
  delete cPayload['variation_for'];

  const res1 = await updateComponent({ body: cPayload, params: { allComponents: true } });

  if (res1.error) throw new Error('Failed to update component');

  return {
    fids: res.data.feature_ids,
  };
};

export const getRawViewportDimension = (fcanvasRef, rawImageW, rawImageH) => {
  const zoom = fcanvasRef.current.getZoom();
  const vpLeft = fcanvasRef.current.viewportTransform[4];
  const vpTop = fcanvasRef.current.viewportTransform[5];
  const vpWidth = fcanvasRef.current.getWidth();
  const vpHeight = fcanvasRef.current.getHeight();

  let rawVpLeft = -(vpLeft / zoom);
  let rawVpTop = -(vpTop / zoom);
  let rawVpRight = -((vpLeft - vpWidth) / zoom);
  let rawVpBottom = -((vpTop - vpHeight) / zoom);

  // make sure within 0-rawImageW and 0-rawImageH
  rawVpLeft = Math.min(Math.max(0, rawVpLeft), rawImageW);
  rawVpTop = Math.min(Math.max(0, rawVpTop), rawImageH);
  rawVpRight = Math.min(Math.max(0, rawVpRight), rawImageW);
  rawVpBottom = Math.min(Math.max(0, rawVpBottom), rawImageH);
  
  // round to integer
  rawVpLeft = Math.ceil(rawVpLeft);
  rawVpTop = Math.ceil(rawVpTop);
  rawVpRight = Math.floor(rawVpRight);
  rawVpBottom = Math.floor(rawVpBottom);

  return {
    left: rawVpLeft,
    top: rawVpTop,
    right: rawVpRight,
    bottom: rawVpBottom,
  };
};

export const handleSceneCopyBatchUngroupedFeatures = async (
  srcFeatures,
  addFeatureRoi,
) => {
  const newFeatureIds = [];
  const newFeatureObjs = [];

  for (const feature of srcFeatures) {
    let payload = {
      step: _.get(feature, 'step', 0),
      product_id: _.get(feature, 'product_id', 0),
      roi: {
        type: 'obb',
        points: [
          {
            x: _.get(feature, 'roi.points[0].x', 0) + sceneCopyRectBottomRightOffset,
            y: _.get(feature, 'roi.points[0].y', 0) + sceneCopyRectBottomRightOffset,
          },
          {
            x: _.get(feature, 'roi.points[1].x', 0) + sceneCopyRectBottomRightOffset,
            y: _.get(feature, 'roi.points[1].y', 0) + sceneCopyRectBottomRightOffset,
          }
        ],
        angle: _.get(feature, 'roi.angle', 0),
      },
      feature_type: _.get(feature, 'feature_type', ''),
      feature_scope: 'product',
      line_item_params: _.get(feature, 'line_item_params', {}),
    }

    payload = removeProfileHeightFromPayload(payload);

    const res = await addFeatureRoi({ body: payload, params: { allComponents: false } });

    if (res.error) throw new Error('Failed to add feature');

    newFeatureIds.push(_.get(res, 'data[0].feature_id', 0));
    newFeatureObjs.push({
      ...res.data[0],
      line_item_params: payload.line_item_params,
    });
  }

  // allow scene to complete reload
  // we want to batch select the new features
  return {
    newFIds: newFeatureIds,
    newFeatureObjs: newFeatureObjs,
  }
};

export const isSolderFeatureAndProfileRoiEnabled = (featureObj) => {
  const isSolder = _.includes(_.keys(featureObj.line_item_params), solderInspection3D);
  if (!isSolder) return false;
  const isProfileEnabled = _.get(featureObj, `line_item_params.${solderInspection3D}.params.${profileHeight}.active`, false) &&
  _.get(featureObj, `line_item_params.${solderInspection3D}.params.${profileWidth}.active`, false) &&
  _.get(featureObj, `line_item_params.${solderInspection3D}.params.${extTop}.active`, false) &&
  _.get(featureObj, `line_item_params.${solderInspection3D}.params.${extBottom}.active`, false);
  return isProfileEnabled;
};

export const getAABBFromRect = ({
  innerPMin,
  innerPMax,
  angle,
}) => {
  const tmpRect = new fabric.Rect({
    left: innerPMin.x - newRectStrokeWidth,
    top: innerPMin.y - newRectStrokeWidth,
    width: innerPMax.x - innerPMin.x + newRectStrokeWidth,
    height: innerPMax.y - innerPMin.y + newRectStrokeWidth,
    strokeWidth: newRectStrokeWidth,
  });

  tmpRect.rotate(angle);

  const aabb = tmpRect.getBoundingRect();
  return {
    pMin: {
      x: _.round(aabb.left + newRectStrokeWidth, 0),
      y: _.round(aabb.top + newRectStrokeWidth, 0),
    },
    pMax: {
      x: _.round(aabb.left + aabb.width, 0 ),
      y: _.round(aabb.top + aabb.height, 0 ),
    }
  };
};

export const getAddMountingFeaturePayload = ({
  innerPMin,
  innerPMax,
  systemMetadata,
  productId,
  isAOI2DSMT = false,
  isAOI3DSMT = false,
}) => {
  return {
    line_item_params: initMountingDefaultLineItemParams(
      _.get(systemMetadata, 'default_line_items', {}),
      isAOI2DSMT,
      isAOI3DSMT,
    ),
    feature_type: `_${_.get(systemMetadata, 'default_component_types[0]', '')}`,
    roi: {
      type: 'obb',
      points: [
        {
          x: _.round(innerPMin.x, 0),
          y: _.round(innerPMin.y, 0),
        },
        {
          x: _.round(innerPMax.x - 1, 0),
          y: _.round(innerPMax.y - 1, 0),
        }
      ],
      angle: 0,
      center: null
    },
    step: 0,
    product_id: Number(productId),
    feature_scope: 'global',
  };
};

export const getAddSolderFeaturePayload = ({
  innerPMin,
  innerPMax,
  systemMetadata,
  productId,
}) => {
  let payload;
  const rectInnerDimension = {
    width: innerPMax.x - innerPMin.x,
    height: innerPMax.y - innerPMin.y,
  };
  let solderDefaultParams = initSolderDefaultLineItemParams(_.get(systemMetadata, 'default_line_items', {}));

  if (isAOI3DSMT) {
    if (rectInnerDimension.width > rectInnerDimension.height) {
      solderDefaultParams = {
        ...solderDefaultParams,
        [solderInspection3D]: {
          ...solderDefaultParams[solderInspection3D],
          params: {
            ...solderDefaultParams[solderInspection3D].params,
            // profile_height: {
            //   ...solderDefaultParams[solderInspection3D].params.profile_height,
            //   param_int: {
            //     ...solderDefaultParams[solderInspection3D].params.profile_height.param_int,
            //     value: _.floor(roi.points[1].y - roi.points[0].y + 1 +
            //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_bottom.param_int.value`, 0) +
            //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_top.param_int.value`, 0), 0),
            //   },
            // },
            profile_width: {
              ...solderDefaultParams[solderInspection3D].params.profile_width,
              param_int: {
                ...solderDefaultParams[solderInspection3D].params.profile_width.param_int,
                value: _.round(rectInnerDimension/2, 0),
              },
            },
          }
        }
      };
    } else {
      solderDefaultParams = {
        ...solderDefaultParams,
        [solderInspection3D]: {
          ...solderDefaultParams[solderInspection3D],
          params: {
            ...solderDefaultParams[solderInspection3D].params,
            // profile_height: {
            //   ...solderDefaultParams[solderInspection3D].params.profile_height,
            //   param_int: {
            //     ...solderDefaultParams[solderInspection3D].params.profile_height.param_int,
            //     value: _.floor(roi.points[1].y - roi.points[0].y + 1 +
            //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_bottom.param_int.value`, 0) +
            //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_top.param_int.value`, 0), 0),
            //   },
            // },
            profile_width: {
              ...solderDefaultParams[solderInspection3D].params.profile_width,
              param_int: {
                ...solderDefaultParams[solderInspection3D].params.profile_width.param_int,
                value: _.round((rectInnerDimension.width)/2, 0),
              },
            },
          }
        }
      };
    }
  }

  if (rectInnerDimension.width > rectInnerDimension.height) {
    const roi = {
      type: 'obb',
      points: [
        {
          x: _.round(innerPMin.x, 0),
          y: _.round(innerPMin.y, 0),
        },
        {
          x: _.round(innerPMax.x - 1, 0),
          y: _.round(innerPMax.y - 1, 0),
        },
      ],
      angle: 0,
      center: null
    };
    payload = {
      step: 0,
      product_id: Number(productId),
      roi,
      feature_type: `_${_.get(systemMetadata, 'default_component_types[1]', '')}`,
      feature_scope: 'product',
      line_item_params: solderDefaultParams,
    };
  } else {
    const bboxPMinPMax = getAABBFromRect({
      innerPMin,
      innerPMax,
      angle: 90
    });
    const roi = {
      type: 'obb',
      points: [
        {
          x: _.round(bboxPMinPMax.pMin.x, 0),
          y: _.round(bboxPMinPMax.pMin.y, 0),
        },
        {
          x: _.round(bboxPMinPMax.pMax.x - 1, 0),
          y: _.round(bboxPMinPMax.pMax.y - 1, 0),
        }
      ],
      angle: 270,
      center: null
    };
    payload = {
      step: 0,
      product_id: Number(productId),
      roi,
      feature_type: `_${_.get(systemMetadata, 'default_component_types[3]', '')}`,
      feature_scope: 'product',
      line_item_params: solderDefaultParams,
    };
  }

  return payload;
};

export const getAddLeadFeaturePayload = ({
  innerPMin,
  innerPMax,
  systemMetadata,
  productId,
  isAOI2DSMT = false,
  isAOI3DSMT = false,
}) => {
  let payload;
  const rectInnerDimension = {
    width: innerPMax.x - innerPMin.x,
    height: innerPMax.y - innerPMin.y,
  };
  const leadDefaultParams = initLeadDefaultLintItemParams(
    _.get(systemMetadata, 'default_line_items', {}),
    isAOI2DSMT,
    isAOI3DSMT,
  );

  if (rectInnerDimension.width >= rectInnerDimension.height) {
    payload = {
      step: 0,
      product_id: Number(productId),
      roi: {
        type: 'obb',
        points: [
          {
            x: _.round(innerPMin.x, 0),
            y: _.round(innerPMin.y, 0),
          },
          {
            x: _.round(innerPMax.x - 1, 0),
            y: _.round(innerPMax.y - 1, 0),
          }
        ],
        angle: 0,
        center: null
      },
      feature_type: `_${_.get(systemMetadata, 'default_component_types[1]', '')}`,
      feature_scope: 'product',
      line_item_params: leadDefaultParams,
    };
  } else {
    const bboxPMinPMax = getAABBFromRect({
      innerPMin,
      innerPMax,
      angle: 90
    });

    payload = {
      step: 0,
      product_id: Number(productId),
      roi: {
        type: 'obb',
        points: [
          {
            x: _.round(bboxPMinPMax.pMin.x + newRectStrokeWidth * 2, 0),
            y: _.round(bboxPMinPMax.pMin.y + newRectStrokeWidth * 2, 0),
          },
          {
            x: _.round(bboxPMinPMax.pMax.x - newRectStrokeWidth, 0),
            y: _.round(bboxPMinPMax.pMax.y - newRectStrokeWidth, 0),
          }
        ],
        angle: 270,
        center: null
      },
      feature_type: `_${_.get(systemMetadata, 'default_component_types[1]', '')}`,
      feature_scope: 'product',
      line_item_params: initLeadDefaultLintItemParams(
        _.get(systemMetadata, 'default_line_items', {}),
        isAOI2DSMT,
        isAOI3DSMT,
      ),
    };
  }

  return payload
};

export const getAddTextVerificationFeaturePayload = ({
  innerPMin,
  innerPMax,
  systemMetadata,
  productId,
}) => {
  const rectInnerDimension = {
    width: innerPMax.x - innerPMin.x,
    height: innerPMax.y - innerPMin.y,
  };

  let roi;

  if (rectInnerDimension.width <= rectInnerDimension.height) {
    const bboxPMinPMax = getAABBFromRect({
      innerPMin,
      innerPMax,
      angle: -90
    });

    roi = {
      type: 'obb',
      points: [
        {
          x: _.round(bboxPMinPMax.pMin.x, 0),
          y: _.round(bboxPMinPMax.pMin.y, 0),
        },
        {
          x: _.round(bboxPMinPMax.pMax.x - 1, 0),
          y: _.round(bboxPMinPMax.pMax.y - 1, 0),
        }
      ],
      angle: 90,
      center: null
    }
  } else {
    roi = {
      type: 'obb',
      points: [
        {
          x: _.round(innerPMin.x, 0),
          y: _.round(innerPMin.y, 0),
        },
        {
          x: _.round(innerPMax.x - 1, 0),
          y: _.round(innerPMax.y - 1, 0),
        },
      ],
      angle: 0,
      center: null
    }
  }

  const payload = {
    step: 0,
    product_id: Number(productId),
    roi,
    feature_type: `_${_.get(systemMetadata, 'default_component_types[2]', '')}_${Date.now()}`,
    feature_scope: 'product',
    line_item_params: initTextVerificationDefaultLineItemParams(_.get(systemMetadata, 'default_line_items', {})),
  };

  return payload;
};

export const getAddBarcodeFeaturePayload = ({
  innerPMin,
  innerPMax,
  systemMetadata,
  productId,
}) => {
  return {
    step: 0,
    product_id: Number(productId),
    roi: {
      type: 'obb',
      points: [
        {
          x: _.round(innerPMin.x, 0),
          y: _.round(innerPMin.y, 0),
        },
        {
          x: _.round(innerPMax.x - 1, 0),
          y: _.round(innerPMax.y - 1, 0),
        }
      ],
      angle: 0,
      center: null
    },
    feature_type: `_${_.get(systemMetadata, 'default_component_types[4]', '')}`,
    feature_scope: 'product',
    line_item_params: initBarcodeScannerDefaultLineItemParams(_.get(systemMetadata, 'default_line_items', {})),
  };
};

export const handleSceneRemoveComponentOnly = async (
  componentObj,
  deleteComponent,
) => {
  const res = await deleteComponent({
    definition_product_id: _.get(componentObj, 'definition_product_id', 0),
    definition_step: _.get(componentObj, 'definition_step', 0),
    region_group_id: _.get(componentObj, 'region_group_id', 0),
  });

  if (res.error) throw new Error('Failed to remove component');

  return;
};

export const handlePolarityRoiUpdate = ({
  rect,
  paramRoi,
  featureObj,
  t,
  updateFeature,
  updateAllFeaturesState,
  componentObj,
  updateGroupAgentParams,
}) => {
  // get the new current polarity roi center
  // rotate around the current feature top left by -feature angle
  // then get the pmin pmax based on this center and submit
  let payload;
  let newInnerDimension;

  let featureCenter = getComponentCenterByRoiDtoObj(_.get(featureObj, 'roi', {}));

  let featureTopLeft = {
    x: featureCenter.x - (_.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1) / 2,
    y: featureCenter.y - (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1) / 2,
  };
  featureTopLeft = rotatePoint(featureTopLeft, _.get(featureObj, 'roi.angle', 0), featureCenter);

  const targetCenter = rotatePoint(rect.getCenterPoint(), -_.get(featureObj, 'roi.angle', 0), featureTopLeft);

  if (rect.scaleX !== 1 || rect.scaleY !== 1) {
    // scaling
    newInnerDimension = {
      width: (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1 + newRectStrokeWidth) * rect.scaleX - newRectStrokeWidth,
      height: (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1 + newRectStrokeWidth) * rect.scaleY - newRectStrokeWidth,
    };
  } else {
    // translate
    newInnerDimension = {
      width: (_.get(paramRoi, 'points[1].x', 0) - _.get(paramRoi, 'points[0].x', 0) + 1),
      height: (_.get(paramRoi, 'points[1].y', 0) - _.get(paramRoi, 'points[0].y', 0) + 1),
    };
  }

  // check if it's out of feature's roi
  // we need to rotate the new rect around the feature center by the -feature angle
  // and get the pmin pmax of the feature rect when it's 0 deg
  const tmpPolarityCenter = rotatePoint(rect.getCenterPoint(), -_.get(featureObj, 'roi.angle', 0), featureCenter);
  const tmpPolarityPMin = {
    x: tmpPolarityCenter.x - newInnerDimension.width / 2,
    y: tmpPolarityCenter.y - newInnerDimension.height / 2,
  };
  const tmpPolarityPMax = {
    x: tmpPolarityCenter.x + newInnerDimension.width / 2 - 1,
    y: tmpPolarityCenter.y + newInnerDimension.height / 2 - 1,
  };
  const tmpFeaturePMin = {
    x: featureCenter.x - (_.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1) / 2,
    y: featureCenter.y - (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1) / 2,
  };
  const tmpFeaturePMax = {
    x: featureCenter.x + (_.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1) / 2,
    y: featureCenter.y + (_.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1) / 2,
  };

  if (tmpPolarityPMin.x < tmpFeaturePMin.x || tmpPolarityPMax.x > tmpFeaturePMax.x ||
    tmpPolarityPMin.y < tmpFeaturePMin.y || tmpPolarityPMax.y > tmpFeaturePMax.y) {
    aoiAlert(t('notification.error.thisRoiHasToBePleacedWithinTheFeatureRoi'), ALERT_TYPES.COMMON_ERROR);
    return;
  }

  // need to convert to feature roi top left's coord system
  const centerInFeatureCoord = {
    x: targetCenter.x - featureTopLeft.x,
    y: targetCenter.y - featureTopLeft.y,
  };

  payload = {
    ...featureObj,
    line_item_params: {
      ...featureObj.line_item_params,
      [mountingInspection2D]: {
        ...featureObj.line_item_params[mountingInspection2D],
        params: {
          ...featureObj.line_item_params[mountingInspection2D].params,
          [polarityRoi]: {
            ...featureObj.line_item_params[mountingInspection2D].params[polarityRoi],
            param_roi: {
              ...featureObj.line_item_params[mountingInspection2D].params[polarityRoi].param_roi,
              points: [
                {
                  x: _.round(centerInFeatureCoord.x - newInnerDimension.width / 2, 0),
                  y: _.round(centerInFeatureCoord.y - newInnerDimension.height / 2, 0),
                },
                {
                  x: _.round(centerInFeatureCoord.x + newInnerDimension.width / 2 - 1, 0),
                  y: _.round(centerInFeatureCoord.y + newInnerDimension.height / 2 - 1, 0),
                }
              ],
              center: null,
              type: 'obb',
              angle: _.get(featureObj, 'roi.angle', 0),
            },
          },
        },
      },
    }
  };

  const submit = async (payload, componentObj) => {
    // if feature not in any component then use update feature ow use update group
    if (!_.isInteger(payload.group_id)) {
      const res = await updateFeature({ body: payload, params: { allComponents: _.isInteger(payload.group_id) } });

      if (res.error) {
        aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
        console.error(res.error.message);
        return;
      }

      // await updateAllFeaturesState([featureObj.feature_id], 'update', [payload]);
      if (!_.isEmpty(res.data)) {
        await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
      }
      return;
    }

    // no need to find the top hirarchy here backend will take care of it so we just use the component id for payload
    const res = await updateGroupAgentParams({
      line_item_params: removeProfileHeightFromPayload(payload).line_item_params,
      product_id: componentObj.definition_product_id,
      step: 0,
      feature_type: payload.feature_type,
      component_id: componentObj.region_group_id,
    });

    if (res.error) {
      aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
      console.error(res.error.message);
      return;
    }

    // TODO: wait for backend's update to provide all related feature dto
    if (!_.isEmpty(res.data)) {
      await updateAllFeaturesState(res.data.feature_ids, 'updateGroupParam', res.data.line_item_params);
    }
  };

  submit(payload, componentObj);
};

export const updatePolarityNMaskROIByFeatureScalingEvent = (featureObj) => {
  // this will be called when feature roi's width or height is sclaed down
  // modify the polarity and mask roi's position or dimension to avoid out of feature roi
  // polarity and mask roi's coordinate system is based on the feature roi's top left corner

  let newFeatureObj = _.cloneDeep(featureObj);
  // Check both translation and scaling for polarity and mask ROIs
  const featureWidth = _.get(featureObj, 'roi.points[1].x', 0) - _.get(featureObj, 'roi.points[0].x', 0) + 1;
  const featureHeight = _.get(featureObj, 'roi.points[1].y', 0) - _.get(featureObj, 'roi.points[0].y', 0) + 1;

  // Update polarity ROI if enabled
  if (isMounting2DPolarityRoiEnabled(featureObj)) {
    const polarityRoiObj = _.get(featureObj, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi`);
    let polarityWidth = _.get(polarityRoiObj, 'points[1].x', 0) - _.get(polarityRoiObj, 'points[0].x', 0) + 1;
    let polarityHeight = _.get(polarityRoiObj, 'points[1].y', 0) - _.get(polarityRoiObj, 'points[0].y', 0) + 1;
    polarityWidth = Math.min(polarityWidth, featureWidth);
    polarityHeight = Math.min(polarityHeight, featureHeight);
    
    // Check if polarity ROI exceeds feature boundaries
    const polarityPMax = {
      x: _.get(polarityRoiObj, 'points[1].x', 0),
      y: _.get(polarityRoiObj, 'points[1].y', 0)
    };
    
    let newPolarityPMin = {
      x: _.get(polarityRoiObj, 'points[0].x', 0),
      y: _.get(polarityRoiObj, 'points[0].y', 0)
    };
    
    // Adjust polarity ROI if it exceeds feature boundaries
    if (polarityPMax.x >= featureWidth) {
      newPolarityPMin.x = Math.max(0, featureWidth - polarityWidth);
    }
    if (polarityPMax.y >= featureHeight) {
      newPolarityPMin.y = Math.max(0, featureHeight - polarityHeight);
    }
    
    const newPolarityPMax = {
      x: newPolarityPMin.x + polarityWidth - 1,
      y: newPolarityPMin.y + polarityHeight - 1
    };
    
    newFeatureObj = _.set(newFeatureObj, `line_item_params.${mountingInspection2D}.params.${polarityRoi}.param_roi.points`, [
      newPolarityPMin,
      newPolarityPMax
    ]);
  }

  // Update mask ROI if enabled
  if (isMounting2DMaskRoiEnabled(featureObj)) {
    const maskRoiObj = _.get(featureObj, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi`);
    let maskWidth = _.get(maskRoiObj, 'points[1].x', 0) - _.get(maskRoiObj, 'points[0].x', 0) + 1;
    let maskHeight = _.get(maskRoiObj, 'points[1].y', 0) - _.get(maskRoiObj, 'points[0].y', 0) + 1;
    maskWidth = Math.min(maskWidth, featureWidth);
    maskHeight = Math.min(maskHeight, featureHeight);
    
    // Check if mask ROI exceeds feature boundaries
    const maskPMax = {
      x: _.get(maskRoiObj, 'points[1].x', 0),
      y: _.get(maskRoiObj, 'points[1].y', 0)
    };
    
    let newMaskPMin = {
      x: _.get(maskRoiObj, 'points[0].x', 0),
      y: _.get(maskRoiObj, 'points[0].y', 0)
    };
    
    // Adjust mask ROI if it exceeds feature boundaries
    if (maskPMax.x >= featureWidth) {
      newMaskPMin.x = Math.max(0, featureWidth - maskWidth);
    }
    if (maskPMax.y >= featureHeight) {
      newMaskPMin.y = Math.max(0, featureHeight - maskHeight);
    }
    
    const newMaskPMax = {
      x: newMaskPMin.x + maskWidth - 1,
      y: newMaskPMin.y + maskHeight - 1
    };
    
    newFeatureObj = _.set(newFeatureObj, `line_item_params.${mountingInspection2D}.params.${maskRoi}.param_roi.points`, [
      newMaskPMin,
      newMaskPMax
    ]);
  }

  return newFeatureObj;
};

export const filterComponentsBySelectionRoi = (components, selectionRoi) => {
  if (!components || !Array.isArray(components) || !selectionRoi) {
    return [];
  }

  const { pMin: selectionPMin, pMax: selectionPMax } = selectionRoi;

  if (!selectionPMin || !selectionPMax) {
    return [];
  }

  return components.filter(component => {
    // Get component's shape properties
    const componentPMin = _.get(component, 'shape.points[0]', null);
    const componentPMax = _.get(component, 'shape.points[1]', null);
    const componentAngle = _.get(component, 'shape.angle', 0);

    if (!componentPMin || !componentPMax) {
      return false;
    }

    // If component has no rotation, do simple rectangle containment check
    if (componentAngle === 0) {
      return (
        componentPMin.x >= selectionPMin.x &&
        componentPMin.y >= selectionPMin.y &&
        componentPMax.x <= selectionPMax.x &&
        componentPMax.y <= selectionPMax.y
      );
    }

    // For rotated components, get the axis-aligned bounding box (AABB)
    // of the rotated component rectangle
    const componentAABB = getAABBFromRect({
      innerPMin: componentPMin,
      innerPMax: componentPMax,
      angle: componentAngle
    });

    // Check if the component's AABB is completely within the selection ROI
    return (
      componentAABB.pMin.x >= selectionPMin.x &&
      componentAABB.pMin.y >= selectionPMin.y &&
      componentAABB.pMax.x <= selectionPMax.x &&
      componentAABB.pMax.y <= selectionPMax.y
    );
  });
};

export const attachModifiedEventToSubArrayBoardSelectionRoi = (
  rect,
  arrayRegisteration,
  step,
  curProduct,
  goldenRegisterArray,
  handleRefetchAllComponents,
  refetchArrayRegisteration,
  t,
) => {
  rect.on('modified', () => {
    const payload = {
      product_id: Number(_.get(curProduct, 'product_id', 0)),
      step,
      selection_roi: _.get(arrayRegisteration, 'selection_roi', {}),
      component_ids: _.get(arrayRegisteration, 'component_ids', []),
    };

    const selectionRoiPMin = {
      x: _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
      y: _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
    };

    const roiDimension = {
      width: _.get(arrayRegisteration, 'selection_roi.points[1].x', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
      height: _.get(arrayRegisteration, 'selection_roi.points[1].y', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
    };

    const array_transforms = _.map(_.get(arrayRegisteration, 'array_transforms', []), (transform) => {
      if (transform.array_index === rect.get('arrayIndex')) {
        const rectCenter = rect.getCenterPoint();
        const rectTopLeft = {
          x: rectCenter.x - roiDimension.width / 2,
          y: rectCenter.y - roiDimension.height / 2,
        };
        return {
          array_index: transform.array_index,
          flip: {
            x: rect.get('xFilped'),
            y: rect.get('yFilped'),
            center: {
              // x: rectCenter.x - selectionRoiPMin.x,
              // y: rectCenter.y - selectionRoiPMin.y,
              x: rectCenter.x,
              y: rectCenter.y,
            },
          },
          rotation: {
            angle: rect.angle,
            center: {
              // x: rectCenter.x - selectionRoiPMin.x,
              // y: rectCenter.y - selectionRoiPMin.y,
              x: rectCenter.x,
              y: rectCenter.y,
            },
          },
          translation: {
            x: rectTopLeft.x - selectionRoiPMin.x,
            y: rectTopLeft.y - selectionRoiPMin.y,
          },
        };
      }
      return transform;
    });

    payload.array_transforms = array_transforms;

    const submit = async (payload) => {
      const res = await goldenRegisterArray(payload);

      if (res.error) {
        console.error('registerGoldenArray error:', _.get(res, 'error.message', ''));
        aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      await refetchArrayRegisteration();

      await handleRefetchAllComponents(
        Number(_.get(curProduct, 'product_id', 0)),
        step,
      );
    };

    submit(payload);
  });
};